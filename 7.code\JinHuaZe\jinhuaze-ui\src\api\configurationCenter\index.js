import request from '@/utils/request'

// 列表  
export function list(params) {
  return request({
    url: '/museum-main/noticeManage/list', 
    method: 'get',
    params
  })
}
//删除
export  const del = params => {
  return request({
    url: '/museum-main/noticeManage/remove',
    method: 'post',
    data: params
  })
}
//新增
export const add = params => {
  return request({
    url: "/museum-main/noticeManage/save",
    method: "post",
    data: params
  });
};
//修改 
export const edit = params => {
  return request({
    url: '/museum-main/noticeManage/update',
    method: 'post',
    data: params
  })
}

