import request from '@/utils/request'

// 列表
export function list(params) {
  return request({
    url: '/museum-main/culturalExplorationArticle/list',
    method: 'get',
    params
  })
}
//删除
export  const del = params => {
  return request({
    url: '/museum-main/culturalExplorationArticle/remove',
    method: 'post',
    data: params
  })
}
//新增
export const add = params => {
  return request({
    url: "/museum-main/culturalExplorationArticle/save",
    method: "post",
    data: params
  });
};
//详情
export function details(params) {
  return request({
    url: '/museum-main/culturalExplorationArticle/detail',
    method: 'get',
    params
  })
}
//修改 
export const edit = params => {
  return request({
    url: '/museum-main/culturalExplorationArticle/update',
    method: 'post',
    data: params
  })
}

