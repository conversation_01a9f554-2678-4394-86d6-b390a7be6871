<template>
  <common-dialog ref="dialog" width="45%" title="审批意见" class="blueDialogScroll">
    <el-form class="forms" :model="formValues" :rules="rules" ref="form" size="small" label-width="110px">
      <el-form-item label="" prop="rejectReason">
        <el-input v-model="formValues.rejectReason" placeholder="请输入审批意见" type="textarea"
                  :autosize="{ minRows: 4}"></el-input>
      </el-form-item>

      <el-form-item class="form-footer">
        <el-button type="primary" @click="submitForm('form')" :disabled="loading" :loading="loading">
          {{ loading ? "提交中..." : "确定" }}
        </el-button>
        <el-button @click="quxiao">取消</el-button>
      </el-form-item>
    </el-form>
  </common-dialog>
</template>

<script>
import { handleRefundCheck } from "@/api/refundOrderReview";
import MyEditor from "@/components/WangEditor";

export default {
  name: 'addShowroom',
  components: {
    MyEditor
  },
  data() {
    return {
      loading: false,
      isManage: false,
      formValues: {
        id: '',
        refundStatus: '2', // 审批结果 1通过 2驳回
        rejectReason: '',
      },
      rules: {
        rejectReason: [
          {required: true, message: '请输入', trigger: 'blur'},
        ],
      }

    };
  },
  methods: {
    quxiao() {
      this.$refs.dialog.dialogVisible = false;
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.loading = true;
          try {
            const { code } = await handleRefundCheck(this.formValues, this.isManage);
            if (code === 200) {
              this.$message({
                message: '保存成功',
                type: 'success'
              });
              this.loading = false;
              this.$refs.dialog.dialogVisible = false;
              this.$router.back()
            }
          } catch {
            this.loading = false;
          }
        } else {
          return false;
        }
      });
    },
    showDialog(id, isManage) {
      this.isManage = isManage
      this.formValues.id = id
      this.$refs.dialog.dialogVisible = true;
    }

  }
}
</script>

<style lang="scss" scoped>
.mb {
  margin-bottom: 40px;
}

.mt {
  margin-top: -40px;
}

//行颜色
::v-deep .el-select-dropdown__item {
  color: #606266 !important;
}

//弹窗高度
::v-deep .el-dialog {
  height: auto !important;
}

//弹窗主体
::v-deep .el-dialog__body {
  overflow-y: auto !important;
}

//底部按钮
.form-footer {
  width: 100%;
  height: 65px;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  margin-bottom: 0;

  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
    margin-top: 15px;
  }
}

//上传图片
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

::v-deep .avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
