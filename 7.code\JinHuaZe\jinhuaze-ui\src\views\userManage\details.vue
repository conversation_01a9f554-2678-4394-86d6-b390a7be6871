<script>
import { getDetails } from "@/api/userManage";
import DetailsOrder from "@/views/order/components/orderDetails.vue";
import logo from '@/assets/logo/logo.png'

export default {
    components: {
        DetailsOrder
    },
    name: "addDialog",
    data () {
        return {
          logo,
            visible: false,
            form: {},//个人信息
            rules: {},
            activities: [],
            srcList: [],
            orderTable: [],//订单列表
            userTable: [],//用户列表
            queryParams: {
                pageNum: 1,
                pageSize: 10,
            },
            total: 0,
            loading: false,
            userId: '',//个人id
        };
    },
    created () {
        this.userId = this.$route.query.userId;
        this.getList();
    },
    mounted () {
    },
    methods: {
        open () {
            this.visible = true;
        },
        // 返回列表
        goBack () {
            this.$router.go(-1);
        },
        // 获取列表
        getList () {
            let params = {
                ...this.queryParams,
                userId: this.userId
            };
            getDetails(params).then(res => {
                this.userTable = res.data.contact;
                this.form = res.data.info;
                this.srcList.push(this.form.avatar);
                this.getOrderList();
            });
        },
        getOrderList () {
            let params = {
                ...this.queryParams,
                userId: this.userId
            };
            getDetails(params).then(res => {
                this.orderTable = res.data.order.rows;
                this.total = res.data.order.total;
            });
        },
    }
}
</script>

<template>
    <div class="content">
        <div class="content-top">
            <el-card class="box-card">
                <div class="card-body">
                    <div class="item-left">
                        <el-image style="width: 120px; height: 120px" :src="form.avatar || logo" :preview-src-list="srcList">
                        </el-image>
                    </div>
                    <div class="item-right">
                        <div class="back-btn">
                            <el-button @click="goBack" size="mini" type="plain">返回列表</el-button>
                        </div>
                        <div class="item-right-detail">
                            <el-descriptions :title="form.name" :column="4">
                                <el-descriptions-item label="用户编号">{{form.id}}</el-descriptions-item>
                                <el-descriptions-item label="手机号">{{form.phone}}</el-descriptions-item>
                                <el-descriptions-item label="授权登录日期">{{form.registerTime}}</el-descriptions-item>
                                <el-descriptions-item label="IP">
                                    {{form.idAddress}}
                                </el-descriptions-item>
                            </el-descriptions>
                        </div>
                    </div>
                </div>
            </el-card>
        </div>
        <div class="content-body">
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <div class="left-icon"></div>
                    <span>常用联系人信息</span>
                </div>
                <div class="card-body">
                    <el-table v-loading="loading" :data="userTable" stripe style="width: 100%">
                        <el-table-column type="index" label="序号" width="70"></el-table-column>
                        <el-table-column prop="name" label="姓名"></el-table-column>
                        <el-table-column prop="phone" label="联系电话"></el-table-column>
                        <el-table-column prop="documentTapeName" label="证件类型"></el-table-column>
                        <el-table-column prop="credentialNo" label="证件号码"></el-table-column>
                    </el-table>
                </div>
            </el-card>
            <el-card class="box-card bottom">
                <div slot="header" class="clearfix">
                    <div class="left-icon"></div>
                    <span>订单记录</span>
                </div>
                <div class="card-body">
                    <el-table v-loading="loading" :data="orderTable" stripe style="width: 100%">
                        <el-table-column type="index" label="序号" width="70"></el-table-column>
                        <el-table-column prop="orderSn" label="订单编号"></el-table-column>
                        <el-table-column prop="addTime" label="下单时间"></el-table-column>
                        <el-table-column prop="orderStatusText" label="订单状态"></el-table-column>
                        <el-table-column prop="orderCategoryText" label="订单类别"></el-table-column>
                        <el-table-column prop="totalAmount" label="订单金额（元）"></el-table-column>
                        <el-table-column prop="discountAmount" label="优惠金额（元）"></el-table-column>
                        <el-table-column prop="actualAmount" label="实付金额（元）"></el-table-column>
                        <el-table-column prop="name" label="预订用户"></el-table-column>
                        <el-table-column prop="phone" label="手机号码" width="110"></el-table-column>
                        <el-table-column prop="refundSn" label="关联退款单号"></el-table-column>
                        <el-table-column prop="refundAmount" label="退款金额"></el-table-column>
                        <el-table-column prop="orderSourceText" label="订单来源"></el-table-column>
                        <el-table-column label="操作" width="100">
                            <template #default="{row}">
                                <el-button size="mini" type="text" icon="el-icon-view"
                                    @click="$refs.detail.showDialog(row.orderSn);">详情</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum"
                        :limit.sync="queryParams.pageSize" @pagination="getOrderList" />
                </div>
            </el-card>
        </div>
        <!-- 订单详情 -->
        <DetailsOrder ref="detail" @getList="getList" />
    </div>
</template>

<style scoped lang="scss">
::v-deep .el-select {
    width: 100% !important;
}
::v-deep .el-card {
    border-radius: 10px;
    border: none;
}
.content-top {
    margin: 15px;
    .card-body {
        display: flex;
        .item-left {
            margin-right: 10px;
            ::v-deep .el-image__preview {
                border-radius: 6px;
            }
        }
        .item-right {
            padding: 10px;
            position: relative;
            .back-btn {
                position: absolute;
                right: 0;
            }
            .item-right-detail {
                ::v-deep .el-descriptions__header {
                    margin-bottom: 30px;
                }
                ::v-deep .el-descriptions__title {
                    font-size: 18px;
                }
                ::v-deep .el-descriptions-item__container,
                ::v-deep .el-descriptions-item__label {
                    font-size: 15px;
                }
            }
        }
    }
}
.content-body {
    margin: 0 15px;
    .box-card {
        display: flex;
        flex-direction: column;
        &.bottom {
            margin-top: 10px;
        }
        .clearfix {
            display: flex;
            align-items: center;
            span {
                font-weight: bold !important;
            }
            .left-icon {
                width: 3px;
                height: 16px;
                margin-right: 4px;
                margin-top: 2px;
                border-radius: 10px;
                background-color: rgb(0, 121, 254);
            }
        }
    }
}
</style>
