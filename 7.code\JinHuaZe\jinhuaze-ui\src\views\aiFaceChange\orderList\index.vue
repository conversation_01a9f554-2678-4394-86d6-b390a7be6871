<template>
  <div class="app-container">
    <el-form v-show="showSearch" :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="手机号码" prop="phone">
        <el-input v-model="queryParams.phone" placeholder="请输入手机号码" maxlength="11" clearable v-input-limit.phone />
      </el-form-item>
      <el-form-item label="场景" prop="sceneId">
        <el-select v-model="queryParams.sceneId" placeholder="请选择场景" clearable @change="onSceneChange">
          <el-option v-for="item in sceneOptions" :key="item.sceneCode" :label="item.sceneName"
            :value="item.sceneCode" />
        </el-select>
      </el-form-item>
      <el-form-item label="服饰" prop="costumeId">
        <el-select v-model="queryParams.costumeId" placeholder="请选择服饰" clearable :disabled="!queryParams.sceneId">
          <el-option v-for="item in costumeOptions" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="已支付" value="1" />
          <el-option label="未支付" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTimes">
        <el-date-picker v-model="queryParams.createTimes" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" style="width: 250px" :picker-options="pickerOptions">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="下单时间" prop="payTimes">
        <el-date-picker v-model="queryParams.payTimes" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" style="width: 250px" :picker-options="pickerOptions">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="2">
        <el-button type="primary" plain icon="el-icon-download" size="mini" @click="handleExport">批量导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
      <el-table-column label="序号" align="center" width="60">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="userId" label="用户编号" align="center" width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="name" label="用户名称" align="center"></el-table-column>
      <el-table-column prop="phone" label="手机号码" align="center" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="sceneName" label="场景" align="center" width="150" show-overflow-tooltip></el-table-column>
      <el-table-column prop="costumeName" label="服饰" align="center" width="150" show-overflow-tooltip></el-table-column>
      <el-table-column prop="statusName" label="状态" align="center"></el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" width="150"
        show-overflow-tooltip></el-table-column>
      <el-table-column prop="payAmount" label="实付金额" align="center">
        <template #default="{row}">
          <span>{{ row.payAmount.toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="payTime" label="下单时间" align="center" width="150" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" align="center" width="100" fixed="right">
        <template #default="{row}">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <detail-dialog ref="detailDialog" />
  </div>
</template>

<script>
import { getSceneOptions, getAiFaceChangeOrderList, getCostumeOptionsWithScene } from "@/api/aiFaceChange";
import { getDatePickerOptions } from "@/utils";
import DetailDialog from "./components/detailDialog.vue";

export default {
  name: "AiFaceChangeOrderList",
  components: {
    DetailDialog
  },
  data() {
    return {
      showSearch: true,
      queryParams: {
        phone: '', // 手机号
        sceneId: '', // 场景id
        costumeId: '', // 服饰id
        status: '',
        createTimeStart: '',
        createTimeEnd: '',
        payTimeStart: '',
        payTimeEnd: '',
        createTimes: [], // 临时变量,做删除
        payTimes: [], // 临时变量,做删除
        pageNum: 1,
        pageSize: 10,
      },
      sceneOptions: [],
      costumeOptions: [],
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions: getDatePickerOptions()
    }
  },
  mounted() {
    this.getList()
    this.getOptions()
  },
  methods: {
    async getOptions() {
      const { data } = await getSceneOptions()
      this.sceneOptions = data
    },
    async getList() {
      this.loading = true
      try {
        // 模拟API调用延迟
        // await new Promise(resolve => setTimeout(resolve, 500))
        // 使用假数据，实际项目中取消注释下面的代码
        const obj = { ...this.queryParams }
        console.log(this.queryParams)
        obj.createTimeStart = obj.createTimes ? obj.createTimes[0] : ''
        obj.createTimeEnd = obj.createTimes ? obj.createTimes[1] : ''
        obj.payTimeStart = obj.payTimes ? obj.payTimes[0] : ''
        obj.payTimeEnd = obj.payTimes ? obj.payTimes[1] : ''
        delete obj.createTimes
        delete obj.payTimes
        const { rows, total } = await getAiFaceChangeOrderList(obj)
        this.tableData = rows
        this.total = total
      } finally {
        this.loading = false
      }
    },
    async onSceneChange(e) {
      this.queryParams.costumeId = ''
      const { data } = await getCostumeOptionsWithScene({ sceneCode: e })
      this.costumeOptions = data
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.queryParams.pageNum = 1
      this.getOptions()
      this.getList()
    },
    handleExport() {
      this.download('/museum-app/ai-face/export', {
        ...this.queryParams
      }, `AI换脸订单信息_${new Date().getTime()}.xlsx`)
    },
    handleDetail(row) {
      // 打开详情弹窗
      this.$refs.detailDialog.open(row)
    }
  }
}
</script>

<style scoped lang="scss">
</style>

