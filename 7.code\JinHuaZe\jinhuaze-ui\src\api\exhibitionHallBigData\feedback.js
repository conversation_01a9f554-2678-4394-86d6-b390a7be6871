import request from '@/utils/request'

// 列表
export function getFeedBackList(params) {
  return request({
    url: '/museum-main/pc/feedback/list',
    method: 'get',
    params
  })
}

///museum-main/pc/feedback/reply
export function replyFeedback(data) {
  return request({
    url: '/museum-main/pc/feedback/reply',
    method: 'post',
    data
  })
}

export function getFeedbackStatistics(params) {
  return request({
    url: '/museum-main/pc/feedback/info-count',
    method: 'get',
    params
  })
}

// /museum-main/pc/feedback/delete
export function delFeedback(params) {
  return request({
    url: '/museum-main/pc/feedback/delete',
    method: 'get',
    params
  })
}
