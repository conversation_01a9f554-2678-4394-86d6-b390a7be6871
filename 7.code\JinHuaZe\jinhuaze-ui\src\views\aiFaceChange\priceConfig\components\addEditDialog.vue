<template>
  <common-dialog :title="title" :visible.sync="visible" width="600px" @confirm="handleSubmit" @cancel="handleCancel">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="名称：" prop="name">
            <el-input v-model="form.name" placeholder="请输入" maxlength="20" v-input-limit.chinese />
          </el-form-item>
        </el-col>

        <el-col :span="24" v-if="priceMode === 'single'">
          <el-form-item label="场景：" prop="sceneCode">
            <el-select v-model="form.sceneCode" placeholder="请选择" style="width: 100%" clearable @change="handleSceneChange" :disabled="isEdit">
              <el-option v-for="item in sceneOptions" :key="item.sceneCode" :label="item.sceneName" :value="item.sceneCode" />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- 服饰字段 - 只在单一价格模式下显示 -->
        <el-col :span="24" v-if="priceMode === 'single'">
          <el-form-item label="服饰：" prop="costumeCode">
            <el-select v-model="form.costumeCode" placeholder="请选择" :disabled="!form.sceneCode || isEdit" style="width: 100%" clearable @change="handleCostumeChange">
              <el-option v-for="item in costumeOptions" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="价格：" prop="price">
            <el-input v-model="form.price" placeholder="请输入" v-input-limit.price>
              <template #append>元</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </common-dialog>
</template>

<script>
import { addAiFaceChangePrice, updateAiFaceChangePrice, getSceneOptions, getCostumeOptionsWithScene, getAiFaceChangePriceDetail, getTemplateByCostumeCode } from "@/api/aiFaceChange";

export default {
  name: "AddEditDialog",
  data() {
    return {
      visible: false,
      title: '',
      isEdit: false,
      priceMode: 'single', // 'default' 或 'single'
      form: {
        id: null,
        name: '',
        sceneCode: '',
        costumeCode: '',
        status: 1,
        price: null
      },
      sceneOptions: [],
      costumeOptions: [],
      rules: {
        name: [
          { required: true, message: '请输入名称' }
        ],
        sceneCode: [
          { required: true, message: '请选择场景', trigger: 'change' }
        ],
        costumeCode: [
          { required: true, message: '请选择服饰', trigger: 'change' }
        ],
        price: [
          { required: true, message: '请输入价格' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.resetForm();
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      const { data: sceneData } = await getSceneOptions();
      this.sceneOptions = sceneData;
    },
    open(row, mode = 'single') {
      this.visible = true;
      this.priceMode = mode; // 设置模式
      this.isEdit = !!row;
      this.title = this.isEdit ? '编辑价格配置' : '新增价格配置';

      this.$nextTick(async () => {
        if (this.isEdit && row.id) {
          try {
            const { data } = await getAiFaceChangePriceDetail({ id: row.id });
            this.form.id = data.id;
            for (const key in data) {
              this.form[key] = data[key];
            }
            this.handleSceneChange(this.form.sceneCode)
          } catch (error) {
            console.error('获取详情失败:', error);
          }
        } else {
          this.resetForm();
        }
      })
    },

    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            const submitData = { ...this.form };
            // 确保价格是数字类型
            if (submitData.price) {
              submitData.price = parseFloat(submitData.price);
            }

            let result;
            if (this.isEdit) {
              // 实际项目中取消注释下面的代码
              result = await updateAiFaceChangePrice(submitData);
            } else {
              // 实际项目中取消注释下面的代码
              result = await addAiFaceChangePrice(submitData);
            }

            if (result.code === 200) {
              this.$message.success(result.msg || '操作成功');
              this.$refs.form.resetFields();
              this.visible = false;
              this.$emit('refresh');
            }
          } catch (error) {
            console.error('操作失败:', error);
          }
        }
      });
    },

    handleCancel() {
      // 确保 visible 被设置为 false 以关闭弹窗
      this.visible = false;
      this.resetForm();
    },

    resetForm() {
      this.form.id = null
      this.$refs.form?.resetFields();
    },

    async handleSceneChange(val) {
      if(!this.isEdit){
        this.form.costumeCode = ''
      }

      const { data } = await getCostumeOptionsWithScene({ sceneCode: val })
      this.costumeOptions = data
    },

    async handleCostumeChange(val) {
      const { data } = await getTemplateByCostumeCode({ costumeCode: val })
      console.log('xxxxxxx', data)
      this.title = data ? '编辑价格配置' : '新增价格配置'
      if (data) {
        for (const key in data) {
          this.form[key] = data[key]
        }
      } else {
        this.form.id = null
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-input-number {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}
</style>
