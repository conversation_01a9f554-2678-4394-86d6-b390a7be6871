<script>
import { getDateList, updateDate } from "@/api/tickets/index";
export default {
  name: "index",
  // components: { AddDialog },
  data() {
    return {
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        condition: ''
      },
      tableData: [],
      total: 0,
      loading: false,
      times: [],
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    /** 导出按钮操作 */
    handleExport() {
      this.download('museum-main/admission-date-list/export', {
        ...this.queryParams
      }, `门票信息_${new Date().getTime()}.xlsx`)
    },
    // 查看详情
    goDetails(row) {
      let query = {
        item: JSON.stringify(row)
      };
      console.log(query, '.传参');
      this.$router.push({ path: 'ticketDetails', query: query });
    },
    getList() {
      getDateList(this.queryParams).then(res => {

          this.tableData = res.rows.map(i=> {
            return {
              ...i,
              quantitySales: parseInt(i.quantitySales * 0.9)
            }
          })
          this.total = res.total;
          // this.loading = false;
        }
      );
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.getList();
    },
    /** 重置 */
    resetQuery() {
      for (var key in this.queryParams) {
        this.queryParams[key] = '';
      }
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.times=[]
      this.getList();
    },
    onAdd() {
      this.$refs.addDialog.open();
    },
    timeValue(v) {
      this.queryParams.startTime = v[0];
      this.queryParams.endTime = v[1];
    },
    changeStatus(row) {
      this.$modal.confirm(`是否${row.status == 0 ? '禁用' : '启用'}该日期`).then(() => {
        let params = {
          id: row.id,
          status: row.status == 0 ? 1 : 0
        }
        updateDate(params).then(res => {
          if (res.code == 200) {
            this.getList();
          }
        })
      }).catch(() => { });

    }
  }
}
</script>

<template>
  <div class="app-container">
    <el-form v-show="showSearch" :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="门票日期">
        <el-date-picker v-model="times" type="daterange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="timeValue">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="2">
        <el-button
          type="primary"
          plain
          icon="el-icon-download"
          size="mini"
          v-hasPermi="['ticketsLiaoBo:menu:export']"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
    </el-row>
<!--    <el-row :gutter="10" class="mb8">-->
<!--      <el-col :span="2">-->
<!--        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="onAdd">新增门票日期</el-button>-->
<!--      </el-col>-->
<!--      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>-->
<!--    </el-row>-->
    <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
      <el-table-column prop="date" label="门票日期"></el-table-column>
      <el-table-column prop="quantitySales" label="门票已销售人次"></el-table-column>
      <el-table-column prop="statusName" label="状态">
      </el-table-column>
      <el-table-column prop="userName" label="创建人"></el-table-column>
      <el-table-column prop="createTime" label="创建时间"></el-table-column>
      <el-table-column prop="updateUserName" label="最后修改人"></el-table-column>
      <el-table-column prop="updateTime" label="修改时间"></el-table-column>
<!--      <el-table-column prop="name" label="操作" width="170">-->
<!--        <template #default="{ row }">-->
<!--          <el-button @click="changeStatus(row)" size="mini" type="text">{{ row.status == 0 ? '禁用' : '启用' }}</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>
    <add-dialog ref="addDialog" @refresh="handleQuery" />
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList" />
  </div>
</template>

<style scoped lang="scss">
::v-deep .el-input--small .el-input__inner {
  width: 280px;
}
</style>
