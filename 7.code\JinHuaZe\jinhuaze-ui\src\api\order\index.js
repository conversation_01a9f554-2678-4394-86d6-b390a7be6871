import request from '@/utils/request'

// 后台订单管理--销售订单   
export function list(params) {
  return request({
    url: '/museum-app/order/web/orderList', 
    method: 'get',
    params
  })
}
//下拉框--订单状态  
export function status() {
  return request({
    url: '/museum-app/order/web/options/orderStatus', 
    method: 'get',
  })
}
//下拉框--订单来源 
export function source() {
  return request({
    url: '/museum-app/order/web/options/orderSource', 
    method: 'get',
  })
}
//后台订单管理--退款订单 
export function refundList(params) {
  return request({
    url: '/museum-app/order/web/refundList', 
    method: 'get',
    params
  })
}
//订单详情  /
export function orderDetail(params) {
  return request({
    url: '/museum-app/order/web/orderDetail', 
    method: 'get',
    params
  })
}
//发起退款
export function webCreateRefund(params) {
  return request({
    url: '/museum-app/order/web/webCreateRefund', 
    method: 'post',
    data:params
  })
}




