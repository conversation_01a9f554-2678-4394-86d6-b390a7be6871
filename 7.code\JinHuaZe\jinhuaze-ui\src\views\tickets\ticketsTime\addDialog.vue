<script>
import { updateTicketDate } from "@/api/tickets";

export default {
  name: "addDialog",
  data() {
    return {
      title:"选择日期",
      visible: false,
      form: {
        startTime:"",
        endTime:"",
        times:[],
      },

      rules: {
        times: [ {required: true, message: '请选择日期' }],
      },
      disableOptions:{
      disabledDate(time) {
          // 8.64e7 毫秒数代表一天
          return time.getTime() < Date.now() - 8.64e7;
        },
    }
    }
  },
  computed: {
  },
  mounted() {
  },
  methods: {
    timeValue(v) {
      this.form.startTime = v[0];
      this.form.endTime = v[1];
    },
    open(row, isView) {

      this.visible = true
    },
    onCancel() {
      this.form.id = null
      this.$refs.form.resetFields()
    },
    onSubmit() {
      this.$refs.form.validate(async valid => {
        if(valid) {
          const { code, msg } = await updateTicketDate(this.form)
          if (code === 200) {
            this.onCancel()
            this.$message.success(msg || '操作成功')
            this.visible = false
            this.$emit('refresh')
          }
        }
      })
    }
  }
}
</script>

<template>
  <common-dialog :title="title" :visible.sync="visible" width="900px" confirm-button-text="确定" @cancel="onCancel" @confirm="onSubmit">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="门票日期" prop="times">
        <el-date-picker v-model="form.times" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="timeValue" :picker-options="disableOptions">
        </el-date-picker>
      </el-form-item>
    </el-form>
  </common-dialog>
</template>

<style scoped lang="scss">
::v-deep .el-select {
  width: 100%!important;
}
</style>
