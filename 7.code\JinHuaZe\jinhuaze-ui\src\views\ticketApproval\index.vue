<script>
export default {
    name: "index",
    data () {
        return {
            showSearch: true,
            queryParams: {
                type: [''],
                pageNum: 1,
                pageSize: 10,
            },
            tableData: [{
                date: '2016-05-02',
                name: '王小虎',
                address: '上海市普陀区金沙江路 1518 弄'
            }, {
                date: '2016-05-04',
                name: '王小虎',
                address: '上海市普陀区金沙江路 1517 弄'
            }, {
                date: '2016-05-01',
                name: '王小虎',
                address: '上海市普陀区金沙江路 1519 弄'
            }, {
                date: '2016-05-03',
                name: '王小虎',
                address: '上海市普陀区金沙江路 1516 弄'
            }],
            total: 22,
            loading: false
        };
    },
    methods: {
        getList () {

        },
        handleQuery () {

        },
        resetQuery () {

        },
        onAdd () {
            this.$refs.addDialog.open();
        },
        delRow (row) {
            this.$confirm('确定要删除该日期吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {

            }).catch(() => {
            });
        }

    }
}
</script>

<template>
    <div class="app-container">
        <el-form v-show="showSearch" :model="queryParams" ref="queryForm" size="small" :inline="true">
            <el-form-item label="关键字" prop="type">
                <el-input placeholder="请输入门票名称" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="2">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" v-hasPermi="['system:menu:add']"
                    @click="onAdd">新增门票价格</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
            <el-table-column prop="name" label="展厅名称"></el-table-column>
            <el-table-column prop="name" label="门票名称"></el-table-column>
            <el-table-column prop="name" label="门票价格"></el-table-column>
            <el-table-column prop="name" label="使用期限"></el-table-column>
            <el-table-column prop="name" label="退票规则"></el-table-column>
            <el-table-column prop="name" label="门票备注"></el-table-column>
            <el-table-column prop="name" label="审批状态"></el-table-column>
            <el-table-column prop="name" label="当前审批人"></el-table-column>
            <el-table-column prop="name" label="驳回原因"></el-table-column>
            <el-table-column prop="name" label="是否为工作票"></el-table-column>
            <el-table-column prop="name" label="操作" width="170">
                <template #default="{row}">
                    <el-button size="mini" type="text" icon="el-icon-edit">编辑</el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="delRow(row)">删除</el-button>
                    <el-button size="mini" type="text" icon="el-icon-view">详情</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
            @pagination="getList" />

        <add-dialog ref="addDialog" />
    </div>
</template>

<style scoped lang="scss">
</style>
