<template>
  <common-dialog ref="dialog" width="61%" title="新增内容" class="blueDialogScroll">
    <el-form class="forms" :model="formValues" :rules="rules" ref="form" size="small" label-width="110px">
      <el-form-item label="文章标题" prop="articleTitle">
        <el-input v-model="formValues.articleTitle" placeholder="请输入" maxlength="50"  ></el-input>
      </el-form-item>
      <el-form-item label="分类" prop="type">
        <el-select v-model="formValues.type" style="width: 100%" placeholder="请选择" clearable>
          <el-option v-for="item in typeList" :key="item.label" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="文章内容" prop="content">
        <my-editor v-model="formValues.content" />
      </el-form-item>
      <el-form-item label="文章封面" prop="articleCover">
        <el-upload ref="upload1" :disabled="disabled" :headers="headers" class="avatar-uploader" :action="uploadUrl" :limit="1" :show-file-list="false" :on-error="handleUploadError" :on-progress="onprogress" :on-success="handleAvatarSuccess" :before-upload="beforeUpload">
          <img v-if="formValues.articleCover" :src="formValues.articleCover" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          <p slot="tip">仅允许上传1张图片，图片小于5MB，支持扩展名:jpg,png</p>
        </el-upload>
      </el-form-item>
      <el-form-item label="精品推荐" prop="recommend" class="mb">
        <el-radio v-model="formValues.recommend" label="1" @input="radioChange">是</el-radio>
        <el-radio v-model="formValues.recommend" label="0">否</el-radio>
      </el-form-item>
      <el-form-item v-if="formValues.recommend=='1'" prop="img2">
        <el-upload ref="upload2" :disabled="disabled2" :headers="headers" class="avatar-uploader" :action="uploadUrl" :limit="1" :show-file-list="false" :on-error="handleUploadErrorT" :on-progress="onprogressT" :on-success="handleAvatarSuccessT" :before-upload="beforeUploadT">
          <img v-if="formValues.selectedImg" :src="formValues.selectedImg" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          <p slot="tip">仅允许上传1张图片，图片小于5MB，支持扩展名:jpg,png</p>
        </el-upload>
        <!-- accept="image/png,image/jpg,image/bmp,image/tif,image/gif,image/apng" -->
      </el-form-item>
      <el-form-item class="form-footer">
        <el-button type="primary" @click="submitForm('form')" :disabled="loading" :loading="loading">
          {{ loading ? "loading..." : "提交" }}
        </el-button>
        <el-button @click="quxiao">取消</el-button>
      </el-form-item>
    </el-form>

  </common-dialog>
</template>

<script>
import { getToken } from "@/utils/auth";
import { add } from "@/api/culExploration/index";
import MyEditor from "@/components/WangEditor";
import { imgURL } from '@/utils/index'
export default {
  name: "addExploration",
  components: {
    MyEditor,
  },
  data() {
    return {
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      uploadUrl: process.env.VUE_APP_BASE_API + "/museum-main/oss/upload",
      loading: false,
      typeList: [
        {
          label: "文化探索",
          value: "1",
        },
        {
          label: "攻略",
          value: "2",
        },
      ],
      disabled: false,
      disabled2: false,
      formValues: {
        articleTitle: "",
        type: "",
        content: "",
        articleCover: "",
        selectedImg: "",
        recommend: "0",
      },
      rules: {
        articleTitle: [{ required: true, message: "请输入", trigger: "blur" }],
        type: [{ required: true, message: "请选择", trigger: "change" }],
        articleCover: [{ required: true, message: "请上传", trigger: "blur" }],
        content: [{ required: true, message: "请输入", trigger: "blur" }],
        recommend: [{ required: true, message: "请选择" }],
      },
    };
  },
  mounted() {},
  methods: {
    beforeUpload(file){
        // const isJPG = file.type === 'image/jpeg';
        const isLt2M = file.size / 1024 / 1024 < 5;
        // if (!isJPG) {
        //   this.$message.error('上传头像图片只能是 JPG 格式!');
        // }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 5MB!');
        }
        return isLt2M;
    },
    beforeUploadT(file){
        // const isJPG = file.type === 'image/jpeg';
        const isLt2M = file.size / 1024 / 1024 < 5;
        // if (!isJPG) {
        //   this.$message.error('上传头像图片只能是 JPG 格式!');
        // }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 5MB!');
        }
        return isLt2M;
    },
    radioChange(v) {},
    quxiao() {
      this.$refs.dialog.dialogVisible = false;
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.loading = true;
          try {
            const res = await add(this.formValues);
            const { code } = res;
            if (code === 200) {
              this.$message({
                message: "保存成功",
                type: "success",
              });
              this.$emit("success");
              this.loading = false;
              this.$refs.dialog.dialogVisible = false;
            }
          } catch {
            this.loading = false;
          }
        } else {
          return false;
        }
      });
    },
    showDialog() {
      this.$refs.dialog.dialogVisible = true;
      this.disabled = false;
      this.disabled2 = false;
      for (var key in this.formValues) {
        this.formValues[key] = "";
      }
    },
    onprogress() {
      this.disabled = true;
      this.loading = true;
    },
    onprogressT() {
      this.disabled2 = true;
      this.loading = true;
    },
    handleAvatarSuccess(res, file) {
      this.formValues.articleCover = process.env.VUE_APP_FILE_URL + res.data;
      this.loading = false;
      this.disabled = false
      this.$refs.upload1.clearFiles();
    },
    handleAvatarSuccessT(res, file) {
      this.formValues.selectedImg  = process.env.VUE_APP_FILE_URL + res.data;
      this.loading = false;
      this.disabled2 = false
      this.$refs.upload2.clearFiles();
    },
    handleUploadError() {
      this.$message.error("图片插入失败");
    },
    handleUploadErrorT() {
      this.$message.error("图片插入失败");
    }
  },
};
</script>

<style lang="scss" scoped>
.mb {
  margin-bottom: 40px;
}
.mt {
  margin-top: -40px;
}

//行颜色
::v-deep .el-select-dropdown__item {
  color: #606266 !important;
}

//弹窗高度
::v-deep .el-dialog {
  height: auto !important;
}

//弹窗主体
::v-deep .el-dialog__body {
  height: 700px !important;
  overflow-y: auto !important;
}

//底部按钮
.form-footer {
  width: 100%;
  height: 65px;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  margin-bottom: 0;

  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
    margin-top: 15px;
  }
}

//上传图片
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
::v-deep .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
::v-deep .avatar {
  width: 100px;
  height: 100px;
  display: block;
}
p {
  margin: 0;
}
</style>
