<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="关键字">
        <el-input
          v-model="queryParams.keyWord"
          placeholder="关键字"
          clearable
          style="width: 240px;"
        />
      </el-form-item>
      <el-form-item label="发布时间">
        <el-date-picker
            v-model="times"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd" 
            @change="timeValue">
        </el-date-picker> 
      </el-form-item>
     
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="add">新增公告</el-button>
      </el-form-item>
    </el-form>

    <el-table ref="tables" v-loading="loading" :data="list">
      <el-table-column label="公告标题" align="center" prop="noticeTitle" :show-overflow-tooltip="true"/>
      <el-table-column label="公告内容" align="center" :formatter="removeHtmlStyle" prop="noticeContent" :show-overflow-tooltip="true" />
      <el-table-column label="发布时间" align="center" prop="createTime" :show-overflow-tooltip="true"/>
      <el-table-column label="发布人" align="center" prop="createPeople" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="edit(scope.row)"
          >编辑</el-button> 
          <el-button
            size="mini"   
            type="text"  
            icon="el-icon-view"
            @click="details(scope.row)"
          >详情</el-button> 
          <el-button
            size="mini"
            type="text"  
            icon="el-icon-delete"
            @click="del(scope.row.id)"
          >删除</el-button> 
        </template>
      </el-table-column>   
    </el-table>
    <pagination 
      v-show="total>0"
      :total="total"  
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

   <AddShowroom ref="add" @success="getList" />
   <EditShowroom ref="edit" @success="getList"/>
   <DetailExploration ref="details" />
  </div>  
</template>

<script>
import { list,del } from "@/api/configurationCenter/index";
import AddShowroom from "./components/addShowroom.vue"
import EditShowroom from "./components/editShowroom.vue"
import DetailExploration from "./components/detailsShowroom.vue"

export default {
  name: "showroom",
  dicts: [],
  components:{
    AddShowroom,
    EditShowroom,
    DetailExploration
  },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyWord:'',
        startTime:'',
        endTime:'',
      },
      times:[],
      limit:0,
    
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
    }; 
  },
  created() {
    this.getList();
  },
  methods: {
    removeHtmlStyle (row, column, cellValue)  {
      const html = cellValue
      const relStyle = /style\s*?=\s*?([‘"])[\s\S]*?\1/g //去除样式
      const relTag = /<.+?>/g //去除标签
      const relClass = /class\s*?=\s*?([‘"])[\s\S]*?\1/g // 清除类名
      let newHtml = ''
      if (html) {
        newHtml = html.replace(relStyle, '')
        newHtml = newHtml.replace(relTag, '')
        newHtml = newHtml.replace(relClass, '')
      }
      return newHtml
    },
    timeValue(v){
       this.queryParams.startTime = v[0]
       this.queryParams.endTime = v[1]
    },
    edit(item){ 
      this.$refs.edit.showDialog(item)
    },
    details(item){
      this.$refs.details.showDialog(item)
    },
    del(id){
       this.$confirm('确认删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal:false,
          type: 'warning'
        }).then(() => {
          del({ids:id}).then((res)=>{
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.getList();
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
    },
    add(){
        this.$refs.add.showDialog()
    },
    async getList(){
      let that = this
      try {
        const res = await list(that.queryParams)
        const { code, rows, total } = res
        if (code === 200) {
            that.list = rows
            that.total = total
        } else {
            that.list = []
        }
        that.loading = false
      } catch {
     
      }
    },

    /** 搜索 */
    handleQuery() {
      this.getList()
    },
    /** 重置 */
    resetQuery() {
        this.queryParams.pageNum = 1
        this.queryParams.pageSize = 10
        this.queryParams.keyWord = ''
        this.times = []
        this.queryParams.startTime = ''
        this.queryParams.endTime = ''
        this.getList()
    }
  }
};
</script>

