<script>
import { getListData } from "@/api/userManage";
export default {
    name: "index",
    data () {
        return {
            showSearch: true,
            queryParams: {
                condition: "",
                pageNum: 1,
                pageSize: 10,
            },
            tableData: [],
            total: 0,
            loading: false
        };
    },
    mounted () {
        this.init();
    },
    methods: {
      /** 导出按钮操作 */
      handleExport() {
        this.download('museum-main/user/pc/export', {
          ...this.queryParams
        }, `用户信息_${new Date().getTime()}.xlsx`)
      },
        // 查看详情
        goDetails (row) {
            let query = { userId: row.id };
            this.$router.push({ path: 'details', query: query });
        },
        async init () {
            const res = await getListData(this.queryParams);
            this.tableData = res.rows;
            this.total = res.total
        },
        handleQuery () {
          this.queryParams.pageNum = 1
            this.init();
        },
        resetQuery () {
            this.queryParams = {
                condition: "",
                pageNum: 1,
                pageSize: 10,
            };
            this.init();
        }
    }
}
</script>

<template>
    <div class="app-container">
        <el-form v-show="showSearch" :model="queryParams" ref="queryForm" size="small" :inline="true">
            <el-form-item label="关键字" prop="type">
                <el-input v-model="queryParams.condition" placeholder="请输入用户编号、用户名称、手机号码" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="2">
          <el-button
            type="primary"
            plain
            icon="el-icon-download"
            size="mini"
            v-hasPermi="['userManage:menu:export']"
            @click="handleExport"
          >导出</el-button>
        </el-col>
      </el-row>
        <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
            <el-table-column prop="id" label="用户编号"></el-table-column>
            <el-table-column prop="name" label="用户名称"></el-table-column>
            <el-table-column prop="phone" label="手机号码"></el-table-column>
            <el-table-column prop="registerTime" label="授权登录日期"></el-table-column>
            <el-table-column prop="idAddress" label="IP归属地"></el-table-column>
            <el-table-column prop="name" label="操作" width="170">
                <template #default="{row}">
                    <el-button @click="goDetails(row)" size="mini" type="text" icon="el-icon-view">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
            @pagination="init" />
    </div>
</template>

<style scoped lang="scss">
::v-deep .el-input--small .el-input__inner {
    width: 300px;
}
</style>
