<script>
import { getListData, getListDetails } from "@/api/pictureView/index";

export default {
    components: {
    },
    name: "addDialog",
    data () {
        return {
            basrUrl: process.env.VUE_APP_FILE_URL,
            // baseUrl,
            visible: false,
            selectionIndex: 0,//是否选中
            url: '',
            srcList: [],
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                status: ""
            },
            total: 0,
            loading: false,
            form: {
                img: []
            },
            cuturalList: [
                { imgList: [], linkUrl: '' },
            ],
            selectImg: '',//选中图片
            imgList: [],
            selectionId: '',//选中图片id
            showBtnGroup: false,//是否展示操作btn
            isSuccess: false,//是否通过
          busy: true
        };

    },
    watch: {

    },

    created () {
        this.getList();
    },
    methods: {
        open () {
            this.visible = true;
        },
        getList (value) {
            getListData(this.queryParams).then(res => {
                // this.tableData = res.rows;
                const newData = res.rows;
                if (newData.length > 0) {
                    this.imgList = [...this.imgList, ...newData];
                }
                if (this.imgList[0].status == 1) {
                    this.isSuccess = true;
                    this.showBtnGroup = false;
                } else if (this.imgList[0].status == 2) {
                    this.isSuccess = false;
                    this.showBtnGroup = false;
                } else {
                    this.showBtnGroup = true;
                }
                this.srcList.push(this.basrUrl + this.imgList[0].imgUrl);
                if (value && this.selectionIndex) {
                    this.selectImg = this.basrUrl + this.imgList[this.selectionIndex].imgUrl;
                } else {
                    this.selectImg = this.basrUrl + this.imgList[0].imgUrl;
                }
                this.total = res.total;
                this.busy = false
            });
        },
        // 选中图片
        checkItem (item, index) {
            this.selectionIndex = index;
            this.selectionId = item.id;
            this.selectImg = this.basrUrl + item.imgUrl;
            this.srcList.push(this.basrUrl + item.imgUrl);
            if (item.status == 1) {
                this.isSuccess = true;
                this.showBtnGroup = false;
            } else if (item.status == 2) {
                this.isSuccess = false;
                this.showBtnGroup = false;
            } else {
                this.showBtnGroup = true;
            }
        },
        // 返回列表
        goBack () {
            this.$router.go(-1);
        },
        // 通过/驳回
        saveForm (value) {
            let params = {
                "id": this.selectionId || this.imgList[0].id,
                "status": value
            };
            getListDetails(params).then((res) => {
                if (res.code == 200) {
                    this.$message({
                        type: 'success',
                        message: '操作成功'
                    });
                    if (value == 1) {
                        this.imgList[this.selectionIndex].status = '1';
                    } else if (value == 2) {
                        this.imgList[this.selectionIndex].status = '2';
                    } else {
                        this.imgList[this.selectionIndex].status = '0';
                    }
                    this.selectionIndex++;
                    for (let item = 0; item < this.imgList.length; item++) {
                        const element = this.imgList[item];
                        if (item == this.selectionIndex) {
                            this.selectImg = this.basrUrl + element.imgUrl;
                            this.selectionId = element.id;
                            if (element.status == 1) {
                                this.isSuccess = true;
                                this.showBtnGroup = false;
                            } else if (element.status == 2) {
                                this.isSuccess = false;
                                this.showBtnGroup = false;
                            } else {
                                this.showBtnGroup = true;
                            }
                        }

                    }
                } else {
                    this.$message({
                        type: 'error',
                        message: '操作失败'
                    });
                }
            });
        },
        // 返回
        handleClose () {
            this.$router.go(-1);
        },
        // 驳回
        reject () {
        },
        load () {
            if (this.queryParams.pageNum * this.queryParams.pageSize >= this.total) {
                return false;
            } else {
                this.queryParams.pageNum++;
            }
            setTimeout(() => {
                this.getList(true);
            }, 500);
        },


    }
}
</script>

<template>
    <div class="content">
        <div class="content-body">
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <div class="left-title">
                        <div class="left-icon"></div>
                        <span>审批详情</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="body-left" v-infinite-scroll="load" :infinite-scroll-disabled="busy" style="overflow:auto">
                        <div v-for="(item,index) in imgList" :key="index" :class="index==selectionIndex ?'item-image border':'item-image'
                        " @click="checkItem(item,index)">
                            <img :src="basrUrl+ item.imgUrl">
                            </img>
                            <div
                                :class="item.status==0?'status-icon orange': item.status==1?'status-icon green':item.status==2?'status-icon red':'status-icon'">
                                {{item.status==0?'待审批': item.status==1?'审批通过':item.status==2?'审批未通过':'-'}}
                            </div>
                        </div>
                    </div>

                    <div class="body-right">
                        <el-image class="leftimg" :src="selectImg" :preview-src-list="srcList">
                        </el-image>
                        <div class="bottom-temp">
                            <el-button type="primary" size="small" @click="saveForm(1)"
                                v-if="!isSuccess || showBtnGroup">审批通过</el-button>
                            <el-button type="primary" plain size="small" @click="saveForm(2)"
                                v-if="isSuccess || showBtnGroup">驳回</el-button>
                        </div>
                    </div>
                </div>
            </el-card>

        </div>
    </div>
</template>

<style scoped lang="scss">
::v-deep .el-select {
    width: 100% !important;
}
::v-deep .el-card {
    border-radius: 10px;
    border: none;
}
::v-deep .el-dialog:not(.is-fullscreen) {
    margin-top: 15vh !important;
}
::v-deep .el-form-item__content {
    margin-left: 0 !important;
}
.left-title {
    display: flex;
    align-items: center;
}

.clearfix {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: space-between;
}
.title-rightbtn {
    position: absolute;
    right: 0px;
}
.bottom-temp {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.bottom-btn {
    display: flex;
    justify-content: center;
}
.top-content {
    display: flex;
    align-items: center;
}

.content-body {
    margin: 15px;
    .card-body {
        display: flex;
        justify-content: space-between;
        .body-left {
            // background-color: red;
            height: 75vh;
            // overflow-y: auto;
            // overflow-x: hidden;
            .item-image {
                position: relative;
                // display: flex;
                // flex-direction: column;
                width: 200px;
                height: 200px;
                border-radius: 4px;
                margin-bottom: 10px;
                padding: 3px;
                &.border {
                    border: 3px solid rgb(255, 111, 0);
                }
                img {
                    width: 100%;
                    height: 100%;
                    border-radius: 5px;
                }
                .status-icon {
                    border-radius: 0 4px 0 4px;
                    position: absolute;
                    top: 3px;
                    right: 3px;
                    padding: 4px 8px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 13px;
                    color: white;
                    border: 2px solid white;
                    &.orange {
                        background-color: #f59a23;
                    }
                    &.green {
                        background-color: green;
                    }
                    &.red {
                        background-color: #b91010;
                    }
                }
            }
        }
    }
    .box-card {
        display: flex;
        flex-direction: column;
        position: relative;
        .bottom-btn {
            position: absolute;
            bottom: 10px;
            left: 50%;
            right: 50%;
            width: 60px;
        }
        .clearfix {
            display: flex;
            align-items: center;
            span {
                font-weight: bold !important;
            }
            .left-icon {
                width: 3px;
                height: 16px;
                margin-right: 4px;
                margin-top: 2px;
                border-radius: 10px;
                background-color: rgb(0, 121, 254);
            }
        }
    }
    .title-btn {
        display: flex;
        align-items: flex-start;
        line-height: 34px;
    }
    .item-card {
        margin-bottom: 10px;
        margin-left: 20px;
        margin-right: 20px;
        position: relative;
        .delete {
            position: absolute;
            top: 0;
            right: 10px;
        }
    }
    .body-right {
        display: flex;
        justify-content: center;
        flex: 1;
        height: 70vh;
        position: relative;
        .bottom-temp {
            position: absolute;
            bottom: 10px;
        }
        .leftimg {
            margin-right: 10px;
        }
    }
}
</style>
