import request from '@/utils/request';

// 用户列表
export function getListData (params) {
    return request({
        url: '/museum-main/user/pc/list',
        method: 'get',
      params
    });
}
// 详情列表
export function getDetails (query) {
    return request({
        url: '/museum-main/user/pc/info?userId=' + query.userId + '&pageNum=' + query.pageNum + '&pageSize=' + query.pageSize,
        method: 'get',
    });
}
