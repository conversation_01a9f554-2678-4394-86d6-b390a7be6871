<script>
import MyEditor from "@/components/WangEditor/index.vue"
import { getDigitalExhibitionHallInfo, saveDigitalExhibitionHallInfo } from "@/api/digitalExhibitionHall";

export default {
  components: { MyEditor },
  data() {
    return {
      // 首次进入不显示取消和编辑按钮。有数据，进入默认是只读，点击编辑可以编辑，显示确定和取消，取消按钮继续只读。
      form: {
        id: null,
        exhibitionName: '',
        exhibitionAddress: '',
        announcement: '',
        purchaseInfo: '',
        usageInfo: '',
        otherInfo: ''
      },
      details: {},
      rules: {
        exhibitionName: [
          { required: true, message: '请输入展厅名称' }
        ],
        exhibitionAddress: [
          { required: true, message: '请输入展厅地址' }
        ],
        announcement: [
          { required: true, message: '请输入展厅图文介绍' }
        ]
      },
      editStatus: false
    }
  },
  watch: {
    editStatus(val) {
      if (!val) {
        this.initData()
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      const { data } = await getDigitalExhibitionHallInfo()
      console.log(data)
      this.details = data
      this.initData()
    },
    initData() {
      for (let key in this.form) {
        if (this.details.hasOwnProperty(key)) {
          this.$set(this.form, key, this.details[key])
        }
      }
    },
    onSubmit() {

      console.log(this.form.announcement)

      // return
      this.$refs.form.validate(async valid => {
        if (valid) {
          const { code, msg } = await saveDigitalExhibitionHallInfo(this.form)
          if (code === 200) {
            this.$message.success(msg)
            await this.init()
            this.editStatus = false
          }
        }
      })
    }
  }
}
</script>

<template>
  <div class="app-container">
    <div class="edit_btn d-flex flex-justify-center">

      <el-button v-if="!form.id || editStatus" type="primary" @click="onSubmit">保存</el-button>
      <el-button v-if="!!form.id" :type="editStatus ? '' : 'primary'" @click="editStatus = !editStatus">{{ editStatus ? '取消' :
        ''}}编辑</el-button>
<!--      <el-button v-if="!!form.id && editStatus" @click="editStatus = false">取消</el-button>-->
    </div>
    <el-form ref="form" :model="form" :rules="rules" label-width="120px" :disabled="!!form.id && !editStatus">
      <div class="area-title">基础信息</div>
      <el-form-item label="展厅名称" prop="exhibitionName">
        <el-input v-model="form.exhibitionName" maxlength="20" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="展厅地址" prop="exhibitionAddress">
        <el-input v-model="form.exhibitionAddress" maxlength="20" placeholder="请输入"></el-input>
      </el-form-item>
      <div class="area-title">展厅介绍</div>
      <el-form-item label="展厅图文介绍" prop="announcement">
        <my-editor v-model="form.announcement" />
      </el-form-item>
      <div class="area-title">购买须知</div>
      <el-form-item label="购买说明" prop="purchaseInfo">
        <el-input type="textarea" v-model="form.purchaseInfo" maxlength="500" show-word-limit placeholder="请输入"
          :rows="6"></el-input>
      </el-form-item>
      <el-form-item label="使用说明" prop="usageInfo">
        <el-input type="textarea" v-model="form.usageInfo" maxlength="500" show-word-limit placeholder="请输入"
          :rows="6"></el-input>
      </el-form-item>
      <el-form-item label="其他说明" prop="otherInfo">
        <el-input type="textarea" v-model="form.otherInfo" maxlength="500" show-word-limit placeholder="请输入"
          :rows="6"></el-input>
      </el-form-item>
      <el-form-item>
        <div class="d-flex flex-justify-center">
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">
.app-container {
  width: 70%;

  .edit_btn {
    position: absolute;
    left: 72%;
  }
}
</style>
