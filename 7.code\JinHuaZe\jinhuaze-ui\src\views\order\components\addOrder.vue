<template>
  <common-dialog ref="dialog" width="40%" title="新增订单" class="blueDialogScroll">
    <el-form class="forms" :model="formValues" :rules="rules" ref="form" size="small" label-width="110px">
        <el-form-item label="订单编号:" prop="bh">
          <div class="flex">
              <div>
                {{ bh }}
                <el-tag type="success">{{ status }}</el-tag>
              </div>
              <el-button type="primary" icon="el-icon-download" size="mini" @click="download">下载工作票</el-button>
          </div>
        </el-form-item>
        <el-form-item label="门票名称:" prop="mc">
             {{ mc }}
        </el-form-item>
        <el-form-item label="订单类别:" prop="lb">
          {{ lb }}
        </el-form-item>
        <el-form-item label="订单类型:" prop="lx">
          {{ lx }}
        </el-form-item>
        <el-form-item label="订单来源:" prop="ly">
          {{ ly }}
        </el-form-item>
        <el-form-item label="门票价格(元):" prop="jg">
          {{ jg }}
        </el-form-item>
        <el-form-item label="门票数量:" prop="sl">
          <el-input-number :disabled="disabled3" v-model="sl" @change="handleChange" :min="1"  label="请输入"></el-input-number>
        </el-form-item>
        <el-form-item label="创建人:" prop="cjr">
          {{ cjr }}
        </el-form-item>
        <el-form-item label="创建时间:" prop="sj">
          {{ sj }}
        </el-form-item>
        <el-form-item label="备注:" prop="bz">
          <el-input
            type="textarea"
            :disabled="disabled2"
            :maxlength="200"
             :rows="8"
            placeholder="请输入内容"
            v-model="formValues.bz">
          </el-input>
        </el-form-item>
        <div class="area-title">操作记录</div>
        <el-table :data="historys">
          <el-table-column label="序号" align="center" type="index" />
          <el-table-column label="操作时间" align="center" prop="2" :show-overflow-tooltip="true" />
          <el-table-column label="操作人" align="center" prop="3" :show-overflow-tooltip="true"/>
          <el-table-column label="操作详情" align="center" prop="4" :show-overflow-tooltip="true" />        
        </el-table>
      <el-form-item class="form-footer">
        <el-button type="primary" @click="submitForm('form')" :disabled="loading" :loading="loading">
          {{ loading ? "提交中..." : "提交订单" }}
        </el-button>
        <el-button @click="quxiao">取消</el-button>
      </el-form-item>
    </el-form>
    <QRCodes ref="qRCodes" />
  </common-dialog>
</template>

<script>
import QRCodes from "./qRCode.vue"
export default {
  name: 'addOrder',
  components: {
    QRCodes
  },
  data() {
    return {
      disabled:false,
      disabled2:false,
      disabled3:false,
      loading:false,
      status:'待使用',
      bh:'GH20240101',
      mc:'辽博数字展厅-工作票',
      lb:'门票',
      lx:'销售',
      ly:'手动创建',
      jg:'0',
      sl:'0',
      cjr:'当前用户',
      sj:'',
      formValues:{
        bz:''
      },
      rules: {
        bz: [
          { required: true, message: '请输入', trigger: 'blur' },
        ]
      },
      historys:[
        {
          2:'2022/09/12 10:28:33',
          3:'李诗岩',
          4:'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
        },
        {
          2:'2022/09/12 10:28:33',
          3:'李诗岩',
          4:'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
        },
        {
          2:'2022/09/12 10:28:33',
          3:'李诗岩',
          4:'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
        },
        {
          2:'2022/09/12 10:28:33',
          3:'李诗岩',
          4:'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
        },
        {
          2:'2022/09/12 10:28:33',
          3:'李诗岩',
          4:'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
        },
        {
          2:'2022/09/12 10:28:33',
          3:'李诗岩',
          4:'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
        },
        {
          2:'2022/09/12 10:28:33',
          3:'李诗岩',
          4:'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
        },
        {
          2:'2022/09/12 10:28:33',
          3:'李诗岩',
          4:'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
        }
      ],
      text:"444444"
      
    }
  },
  mounted() {
   
  },
  methods: {
    download(){
      this.$refs.qRCodes.showDialog(this.text,'addOrder')
    },
    handleChange(v){
      this.sl = v
    },
    quxiao(){
      this.$refs.dialog.dialogVisible = false
    },
    submitForm(formName) {
    
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
      
          } catch {
   
           
          }
        } else {
        
          return false;
        }
      });
    },
    showDialog(v) {
      this.sj = v   //时间
      this.formValues.bz = ''  //备注
      this.sl = ''  //数量
      this.$refs.dialog.dialogVisible = true
      
    },
   
  }
}
</script>

<style lang="scss" scoped>



//行颜色
::v-deep .el-select-dropdown__item {
  color: #606266 !important;
}

//弹窗高度
::v-deep .el-dialog {
  height:auto !important;
}

//弹窗主体
::v-deep .el-dialog__body {
  overflow-y: auto !important;
}

//底部按钮
.form-footer {
  width: 100%;
  height: 65px;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  margin-bottom: 0;

  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
    margin-top: 15px;
  }
}
.flex {
  display: flex;
  justify-content: space-between;
}
//下载按钮
::v-deep .flex .el-button--primary{
    background-color: #1890ff !important;
    border-color: #1890ff !important;
  }

</style>
