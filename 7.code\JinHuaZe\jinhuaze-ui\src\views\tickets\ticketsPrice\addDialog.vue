<script>
import { getDict } from "@/api/common";
import { getDigitalExhibitionHallDict, updateTicketPrice } from "@/api/tickets";

export default {
  name: "addDialog",
  data() {
    const minOne = (rules, value, cb) => {
      if (value < 1) {
        return cb(new Error("请输入正整数"))
      }
      return cb()
    }
    const childMax = (rules, value, cb) => {
      if (Number(value) > Number(this.form.visitCount)) {
        return cb(new Error("儿童需小于等于参观人数"))
      }
      return cb()
    }
    return {
      visible: false,
      isView: false,
      form: {
        id: null,
        showroomId: '',
        // isWork: 2,
        admissionName: '',
        price: '',
        serviceLife: '',
        refundRules: '',
        visitCount: '',
        childrenCount: '',
        admissionRemark: ''
      },
      rules: {
        showroomId: [ { required: true, message: '请选择展厅' }],
        // isWork: [ { required: true, message: '请选择是否为工作票' }],
        admissionName: [ { required: true, message: '请输入门票名称' }],
        price: [ { required: true, message: '请输入门票价格' }],
        serviceLife: [ { required: true, message: '请选择使用期限' }],
        refundRules: [ { required: true, message: '请选择退票规则' }],
        visitCount: [
          { required: true, message: '请输入参观人数' },
          { validator: minOne },
        ],
        childrenCount: [
          { required: true, message: '请输入儿童人数' },
          { validator: childMax }
        ],
        admissionRemark: [ { required: true, message: '请输入门票备注' }],
      },

      activities: [{
        content: '审批人',
        avatar: 'https://tvax2.sinaimg.cn/large/650694a1ly1hq7q12owg8j20zk0qon2e.jpg'
      }, {
        content: '最终审批人',
        avatar: 'https://tvax2.sinaimg.cn/large/650694a1ly1hq6nt7vsr9j20xc0p040p.jpg'
      }],
      lifeOptions: [],
      refundOptions: [],
      digitalExhibitionHallOptions: []
    }
  },
  computed: {
    title() {
      return this.isView ? '门票价格详情' : this.form.id ? '编辑门票价格' : '新增门票价格'
    }
  },
  mounted() {
    this.getDict()
  },
  methods: {
    async getDict() {
      const { data: lifeOptions } = await getDict('service_life')
      this.lifeOptions = lifeOptions
      const { data: refundOptions } = await getDict('refund_rules')
      this.refundOptions = refundOptions
      const { data:digitalExhibitionHallOptions } = await getDigitalExhibitionHallDict()
      this.digitalExhibitionHallOptions = digitalExhibitionHallOptions
    },
    open(row, isView) {

      this.visible = true
      this.isView = isView
      this.$nextTick(() => {
        if (row) {
          for (let key in this.form) {
            if (row.hasOwnProperty(key)) {
              this.$set(this.form, key, row[key])
            }
          }
        }
      })
    },
    onCancel() {
      this.form.id = null
      this.$refs.form.resetFields()
    },
    onSubmit() {
      this.$refs.form.validate(async valid => {
        if(valid) {
          const { code, msg } = await updateTicketPrice(this.form, !!this.form.id)
          if (code === 200) {
            this.onCancel()
            this.$message.success(msg || '操作成功')
            this.visible = false
            this.$emit('refresh')
          }
        }
      })
    }
  }
}
</script>

<template>
  <common-dialog :title="title" :visible.sync="visible" :is-view="isView" width="900px" confirm-button-text="提交审核" @cancel="onCancel" @confirm="onSubmit">

    <el-form ref="form" :model="form" :rules="rules" label-width="120px" :disabled="isView">
      <div class="area-title">{{ title }}</div>
      <el-row>
        <el-col :span="11">
          <el-form-item label="展厅名称" prop="showroomId">
            <el-select v-model="form.showroomId" clearable style="width: 350px">
              <el-option v-for="item in digitalExhibitionHallOptions" :key="item.id" :label="item.exhibitionName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="11">
          <el-form-item label="门票名称" prop="admissionName">
            <el-input v-model="form.admissionName" placeholder="请输入" maxlength="10" />
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="门票价格" prop="price">
            <el-input v-model="form.price" v-number-input.decimal="form.price" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="使用期限" prop="serviceLife">
            <el-select v-model="form.serviceLife" clearable style="width: 350px">
              <el-option v-for="item in lifeOptions" :key="item.dictCode" :label="item.dictLabel" :value="item.dictValue"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="退票规则" prop="refundRules">
            <el-select v-model="form.refundRules" clearable style="width: 350px">
              <el-option v-for="item in refundOptions" :key="item.dictCode" :label="item.dictLabel" :value="item.dictValue"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="参观人数" prop="visitCount">
            <el-input v-model="form.visitCount" v-number-input="form.visitCount" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="儿童人数" prop="childrenCount">
            <el-input v-model="form.childrenCount" v-number-input="form.childrenCount" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="门票备注" prop="admissionRemark">
            <el-input v-model="form.admissionRemark" placeholder="请输入" maxlength="50" />
          </el-form-item>
        </el-col>
      </el-row>

      <template v-if="1 === 2">
        <div class="area-title">审批流程</div>

        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in activities"
            :key="index"
            type="primary"
            :hide-timestamp="true"
          >
            <div class="mb8">{{activity.content}}</div>
            <el-avatar :src="activity.avatar"></el-avatar>
          </el-timeline-item>
        </el-timeline>
      </template>
    </el-form>
  </common-dialog>
</template>

<style scoped lang="scss">
::v-deep .el-select {
  width: 100%!important;
}
</style>
