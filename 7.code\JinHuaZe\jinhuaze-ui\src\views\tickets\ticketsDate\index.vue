<template>
  <div class="content">
    <div class="content-body">
      <el-card class="box-card" :loading="true">
        <div slot="header" class="clearfix">
          <div class="left-icon"></div>
          <span>门票日期维护</span>
        </div>
        <!-- <div class="checkbox">
          <div>门票日期设置</div>
          <div class="group">
            <el-checkbox-group v-model="checkedCities" @change="handleCheckedCitiesChange">
              <el-checkbox v-for="city in cities" :label="city" :key="city">{{city}}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="first">
          <div class="first-left">排除日期设置</div>
          <div class="first-right" @click="add(1)">+添加排除日期</div>
        </div>
        <div class="table">
          <el-table :data="tableData" stripe border height="300">
            <el-table-column type="index" width="120" label="序号" align="center"></el-table-column>
            <el-table-column prop="dateFormat" label="排除日期" align="center"></el-table-column>
            <el-table-column label="操作" align="center" width="120">
              <template #default="{row, $index}">
                <el-popconfirm title="确定要删除该日期设置吗？" @confirm="del($index,1)">
                  <el-button slot="reference" size="mini" type="text" icon="el-icon-delete">删除</el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div> -->
        <div class="first">
          <div class="first-left">时段设置</div>
          <div class="first-right" @click="add(2)">+添加时段</div>
        </div>
        <div class="table">
          <el-table :data="tableDataT" stripe border height="300">
            <el-table-column type="index" width="120" label="序号" align="center"></el-table-column>
            <el-table-column prop="timeFormat" label="门票时段" align="center"></el-table-column>
            <el-table-column label="操作" align="center" width="120">
              <template #default="{row, $index}">
                <el-popconfirm title="确定要删除该时段设置吗？" @confirm="del($index,2)">
                  <el-button slot="reference" size="mini" type="text" icon="el-icon-delete">删除</el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="save">
          <el-button type="primary" size="small" @click="save">保存</el-button>
        </div>
      </el-card>
    </div>
    <!-- 排除日期弹窗 -->
    <el-dialog title="请输入" :visible.sync="dialogVisible">
      <div style="width: 100%;text-align: center;">
        <el-date-picker is-range :picker-options="pickerOptions" v-model="datePicker" style="width: 300px" value-format="yyyy-MM-dd" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" placeholder="请选择日期范围">
        </el-date-picker>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="queding(1)">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 时段弹窗 -->
    <el-dialog title="请输入" :visible.sync="dialogVisibleT">
      <div style="width: 100%;text-align: center;">
        <el-time-select placeholder="起始时间" v-model="timeBefore" :picker-options="{
            start: '08:30',
            step: '00:15',
            end: '18:30'
          }">
        </el-time-select>
        <el-time-select placeholder="结束时间" v-model="timeAfter" :picker-options="{
            start: '08:30',
            step: '00:15',
            end: '18:30',
            minTime: timeBefore
          }">
        </el-time-select>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleT = false">取 消</el-button>
        <el-button type="primary" @click="queding(2)">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>
<script>
const cityOptions = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];
import { save, getList } from "@/api/tickets/index";
export default {
  name: "index",
  components: {},
  data() {
    return {
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now();
        }, // 禁用日期
        selectableRange: `${Date().split(" ")[4]} - 23:59:59`, // 打开默认当前时间
      },
      checkedCities: [],
      cities: cityOptions, //门票日期设置列表
      tableData: [], //排除日期列表
      tableDataT: [], //时段列表
      timeBefore: null, //时段开始时间
      timeAfter: null, //时段结束时间
      dialogVisible: false, //排除日期弹窗
      dialogVisibleT: false, //时段弹窗
      datePicker: [], //排除日期
      saveDatePicker: [], //添加排除日期数组集合
      delDatePicker: [], //删除排除日期数组结合
      saveDatePickerT: [], //添加时段数组集合
      delDatePickerT: [], //删除时段数组结合
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      getList().then((res) => {
        this.checkedCities = res.data.weekList;
        this.tableData = res.data.admissionFilterateDateList;
        this.tableDataT = res.data.admissionTimeList;
      });
    },
    //门票日期设置
    handleCheckedCitiesChange(v) {
      this.checkedCities = v;
    },
    //添加（1：排除日期 2：时段）
    add(v) {
      if (v == 1) {
        this.datePicker = [];
        this.dialogVisible = true;
      } else if (v == 2) {
        this.timeAfter = null;
        this.timeBefore = null;
        this.dialogVisibleT = true;
      }
    },
    //确定
    queding(v) {
      if (v == 1) {
        if (this.datePicker.length == 0) {
          this.$message({
            message: "请选择日期",
            type: "warning",
          });
        } else {
          this.dialogVisible = false;
          this.tableData.push({
            id: "",
            dateFormat:
              this.datePicker[0] + " " + "~" + " " + this.datePicker[1],
          });
          this.saveDatePicker.push({
            id: "",
            startDate: this.datePicker[0],
            endDate: this.datePicker[1],
          });
        }
      } else if (v == 2) {
        if (this.timeBefore == "" || this.timeAfter == "") {
          this.$message({
            message: "请选择时段",
            type: "warning",
          });
        } else {
          this.dialogVisibleT = false;
          this.tableDataT.push({
            id: "",
            timeFormat: this.timeBefore + " " + "~" + " " + this.timeAfter,
            startTime: this.timeBefore,
            endTime: this.timeAfter,
          });
          // this.saveDatePickerT.push({
          //   id: "",
          //   startTime: this.timeBefore,
          //   endTime: this.timeAfter,
          // });
        }
      }
    },
    //删除（1：排除日期 2：时段）
    del(index, num) {
      if (num == 1) {
        this.delDatePicker.push({
          id: this.tableData[index].id,
          startDate: this.tableData[index].dateFormat.substring(0, 10),
          endDate: this.tableData[index].dateFormat.substring(13, 23),
        });
        this.tableData.splice(index, 1);
        this.saveDatePicker = this.saveDatePicker.filter((item1) => {
          return !this.delDatePicker.some(
            (item2) =>
              item2.startDate === item1.startDate &&
              item2.endDate === item1.endDate &&
              item2.id === item1.id
          );
        });
        console.log(this.saveDatePicker,'1');
      } else if (num == 2) {
        this.delDatePickerT.push({
          id: this.tableDataT[index].id,
          startTime: this.tableDataT[index].timeFormat.substring(0, 5),
          endTime: this.tableDataT[index].timeFormat.substring(8, 13),
        });
        this.tableDataT.splice(index, 1);
        this.saveDatePickerT = this.saveDatePickerT.filter((item1) => {
          return !this.delDatePickerT.some(
            (item2) =>
              item2.startTime === item1.startTime &&
              item2.endTime === item1.endTime &&
              item2.id === item1.id
          );
        });
      }
    },
    //保存
    save() {
      // console.log(this.checkedCities,'this.checkedCities');
      // if ((this.checkedCities.length == 0)) {
      //   this.$message({
      //     message: "请选择门票日期设置",
      //     type: "warning",
      //   });
      // } else {
        // let params = {
        //   weekList: this.checkedCities,
        //   admissionFilterateDates: this.saveDatePicker,
        //   deleteFilterateIds: this.delDatePicker,
        //   admissionTimeList: this.saveDatePickerT,
        //   deleteTimeIds: this.delDatePickerT,
        // };
        save(this.tableDataT).then((res) => {
          if (res.code == 200) {
            this.$message({
              message: "保存成功",
              type: "success",
            });
            this.getList();
            this.saveDatePicker = []
            this.saveDatePickerT = []
            this.delDatePickerT = []
            this.delDatePicker = []
          }
        });
      }
    // },
  },
};
</script>
<style scoped lang="scss">
.content-body {
  margin: 15px;
  .second-title {
    margin-left: 20px;
    margin-bottom: 20px;
    margin-right: 10px;
  }
  .box-card {
    display: flex;
    flex-direction: column;
    position: relative;
    .bottom-btn {
      position: absolute;
      bottom: 10px;
      left: 50%;
      right: 50%;
      width: 60px;
    }
    .clearfix {
      display: flex;
      align-items: center;
      span {
        font-weight: bold !important;
      }
      .left-icon {
        width: 3px;
        height: 16px;
        margin-right: 4px;
        margin-top: 2px;
        border-radius: 10px;
        background-color: rgb(0, 121, 254);
      }
    }
    .checkbox {
      display: flex;
      align-items: center;
      font-size: 16px;
      height: 16px;
      line-height: 16px;
      & :first-child {
        color: #303133;
      }
      .group {
        height: 16px;
        margin-left: 40px;
      }
    }
    .first {
      display: flex;
      align-items: center;
      font-size: 16px;
      margin: 40px 0 20px 0;
      .first-left {
        height: 16px;
        line-height: 16px;
        color: #303133;
        margin-right: 40px;
      }
      .first-right {
        height: 16px;
        line-height: 16px;
        font-size: 14px;
        color: #0079fe;
        cursor: pointer;
      }
    }
    .table {
      width: auto;
      ::v-deep .el-table td.el-table__cell div {
        margin: 0 20px !important;
      }
    }
    .save {
      width: 100%;
      margin: 80px;
      text-align: center;
    }
  }
}
//时段选择器
::v-deep .el-date-editor.el-input {
  margin: 0 20px !important;
}
</style>
