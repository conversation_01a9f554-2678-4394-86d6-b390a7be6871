import request from '@/utils/request';

// 审批列表
export function getListData (data) {
    return request({
        url: '/museum-main/work/list?status=' + data.status + '&pageNum=' + data.pageNum + '&pageSize=' + data.pageSize,
        method: 'get',
    });
}
// 审批/反审批
export function getListDetails (data) {
    return request({
        url: '/museum-main/work/edit-work-status',
        method: 'post',
        data
    });
}