<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="销售订单" name="first">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
          <el-form-item label="订单编号">
            <el-input v-model="queryParams.orderSn" placeholder="销售单号" clearable/>
          </el-form-item>
          <el-form-item label="预订用户">
            <el-input v-model="queryParams.name" placeholder="预订用户" clearable/>
          </el-form-item>
          <el-form-item label="手机号码">
            <el-input v-model="queryParams.phone" placeholder="手机号码" clearable/>
          </el-form-item>
          <!--          新加-->
          <el-form-item label="用户编号">
            <el-input v-model="queryParams.userId" placeholder="用户编号" clearable/>
          </el-form-item>
          <el-form-item label="门票名称">
            <el-input v-model="queryParams.admissionName" placeholder="门票名称" clearable/>
          </el-form-item>
          <!--          -->
          <el-form-item label="下单时间">
            <el-date-picker v-model="times" type="daterange" range-separator="至" start-placeholder="开始日期"
                            end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="timeValue">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="订单状态">
            <el-select v-model="queryParams.orderStatus" style="width: 100%" placeholder="订单状态" clearable>
              <el-option v-for="item in listStatus" :key="item.label" :label="item.label" :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="订单来源">
            <el-select v-model="queryParams.orderSourceList" style="width: 100%" placeholder="订单来源" clearable multiple>
              <el-option v-for="item in listSource" :key="item.label" :label="item.label" :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery(1)">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery(1)">重置</el-button>
          </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
          <el-col :span="2">
            <el-button
              type="primary"
              plain
              icon="el-icon-download"
              size="mini"
              v-hasPermi="['order:first:export']"
              @click="handleExport"
            >导出
            </el-button>
          </el-col>
        </el-row>
        <el-table ref="tables" v-loading="loading" :data="list" v-if="activeName == 'first'">
          <el-table-column label="订单编号" align="center" prop="orderSn" :show-overflow-tooltip="true"/>
          <el-table-column label="下单时间" align="center" prop="addTime" :show-overflow-tooltip="true"/>
          <el-table-column label="订单状态" align="center" prop="orderStatusText" :show-overflow-tooltip="true"/>
          <el-table-column label="订单类别" align="center" prop="orderCategoryText" :show-overflow-tooltip="true"/>
          <el-table-column label="订单总金额" align="center" prop="totalAmount" width="130"
                           :show-overflow-tooltip="true"/>
          <el-table-column label="优惠金额" align="center" prop="discountAmount" :show-overflow-tooltip="true"/>
          <el-table-column label="实付金额" align="center" prop="actualAmount" :show-overflow-tooltip="true"/>
          <el-table-column label="预订用户" align="center" prop="name" :show-overflow-tooltip="true"/>
          <el-table-column label="用户编号" align="center" prop="createBy" :show-overflow-tooltip="true"/>
          <el-table-column label="手机号码" align="center" prop="phone" :show-overflow-tooltip="true"/>
          <el-table-column label="订单来源" align="center" prop="orderSourceText" :show-overflow-tooltip="true"/>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-view" @click="details(scope.row.orderSn,1)">详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                    @pagination="getList(1)"/>
      </el-tab-pane>

      <el-tab-pane label="退款订单" name="second">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="120px">
          <el-form-item label="退款单号">
            <el-input v-model="queryParamsT.refundSn" placeholder="退款单号" clearable/>
          </el-form-item>
          <el-form-item label="退款申请人">
            <el-input v-model="queryParamsT.refundApplyContactsName" placeholder="退款申请人" clearable/>
          </el-form-item>
          <el-form-item label="关联销售订单号">
            <el-input v-model="queryParamsT.orderSn" placeholder="关联销售订单号" clearable/>
          </el-form-item>
          <!--          新加-->
          <el-form-item label="用户编号">
            <el-input v-model="queryParamsT.userId" placeholder="用户编号" clearable/>
          </el-form-item>
          <el-form-item label="门票名称">
            <el-input v-model="queryParamsT.admissionName" placeholder="门票名称" clearable/>
          </el-form-item>
          <!--          -->
          <el-form-item label="申请退款时间">
            <el-date-picker v-model="timesT" type="daterange" range-separator="至" start-placeholder="开始日期"
                            end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="timeValueT">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="订单状态">
            <el-select v-model="queryParamsT.orderStatus" placeholder="订单状态" clearable>
              <el-option label="退款中" value="300"/>
              <el-option label="已退款" value="301"/>
              <el-option label="退款失败" value="302"/>
            </el-select>
          </el-form-item>
          <el-form-item label="订单来源">
            <el-select v-model="queryParamsT.orderSourceList" style="width: 100%" placeholder="订单来源" clearable multiple>
              <el-option v-for="item in listSource" :key="item.label" :label="item.label" :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery(2)">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery(2)">重置</el-button>
          </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
          <el-col :span="2">
            <el-button
              type="primary"
              plain
              icon="el-icon-download"
              size="mini"
              v-hasPermi="['order:second:export']"
              @click="handleExport"
            >导出
            </el-button>
          </el-col>
        </el-row>
        <el-table ref="tables" v-loading="loading" :data="list" v-if="activeName == 'second'">
          <el-table-column label="退款单号" align="center" prop="refundSn" :show-overflow-tooltip="true"/>
          <el-table-column label="申请退款时间" align="center" prop="refundApplyTime" :show-overflow-tooltip="true"/>
          <el-table-column label="订单状态" align="center" prop="orderStatusText" :show-overflow-tooltip="true"/>
          <el-table-column label="申请退款金额" align="center" prop="refundAmount" :show-overflow-tooltip="true"/>
          <el-table-column label="订单来源" align="center" prop="orderSourceText" :show-overflow-tooltip="true"/>
          <el-table-column label="原订单金额（元）" align="center" prop="totalAmount" :show-overflow-tooltip="true"/>
          <el-table-column label="退款申请人" align="center" prop="refundApplyContactsName"
                           :show-overflow-tooltip="true"/>
          <el-table-column label="用户编号" align="center" prop="refundApplyContactsId" :show-overflow-tooltip="true"/>
          <el-table-column label="关联销售订单号" align="center" prop="orderSn" :show-overflow-tooltip="true"/>
          <el-table-column label="当前处理人" align="center" prop="executeUserName" :show-overflow-tooltip="true"/>
          <el-table-column label="驳回原因" align="center" prop="rejectReason" :show-overflow-tooltip="true"/>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-view" @click="details(scope.row.orderSn,2)">详情
              </el-button>
              <el-button v-if="scope.row.orderStatus== 302" size="mini" type="text" icon="el-icon-s-check"
                         @click="details(scope.row.orderSn,3)">重新提交
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="queryParamsT.pageNum"
                    :limit.sync="queryParamsT.pageSize" @pagination="getList(2)"/>
      </el-tab-pane>
    </el-tabs>
    <refundDialog ref="refunds" @success="getList(2)"/>
    <DetailsOrder ref="detail" @getList="getList(1)"/>
    <DetailsOrderT ref="detailT" @getList="getList(2)"/>


  </div>
</template>

<script>
import {list, refundList, status, source} from "@/api/order/index";
import refundDialog from "./components/refundDialog.vue"
import DetailsOrder from "./components/orderDetails.vue";
import DetailsOrderT from "./components/orderDetailsT.vue";

export default {
  name: "orderList",
  dicts: [],
  components: {
    refundDialog,
    DetailsOrder,
    DetailsOrderT,
  },
  data() {
    return {
      activeName: "first",
      listStatus: [],
      listType: [],
      listSource: [],
      queryParams: {
        refundSn: '',
        orderSn: '',
        name: '',
        phone: '',
        addTimeStart: '',
        addTimeEnd: '',
        orderStatus: '',
        orderSource: '',
        pageNum: 1,
        pageSize: 10
      },
      queryParamsT: {
        refundSn: '',
        orderSn: '',
        refundApplyContactsName: '',
        refundApplyTimeStart: '',
        refundApplyTimeEnd: '',
        orderStatus: '',
        orderSource: '',
        refundSource: '',
        pageNum: 1,
        pageSize: 10
      },
      times: [],
      timesT: [],
      limit: 0,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
    };
  },
  created() {
    this.getStatus();  //订单状态
    this.source();     //订单来源
    this.getList(1);   //销售订单
  },
  methods: {
    /** 导出按钮操作 */
    handleExport() {
      let url='museum-app/order/web/orderList/export'
      let queryParams=this.queryParams
      if(this.activeName === 'second'){
        url='museum-app/order/web/orderList/exportRefund'
        queryParams=this.queryParamsT
      }
      this.download(url, {
        ...queryParams
      }, `${this.activeName === 'first' ? '销售' : '退款'}订单信息_${new Date().getTime()}.xlsx`)
    },
    getStatus() {
      status().then(response => {
        this.listStatus = response.data;
      });
    },
    source() {
      source().then(response => {
        this.listSource = response.data;
      });
    },
    handleClick(tab, event) {
      if (tab.index == 0) {
        this.getList(1);
      } else if (tab.index == 1) {
        this.getList(2);
      }
    },
    timeValue(v) {
      this.queryParams.addTimeStart = v[0] + ' 00:00:00';
      this.queryParams.addTimeEnd = v[1] + ' 23:59:59';
    },
    timeValueT(v) {
      this.queryParamsT.refundApplyTimeStart = v[0] + ' 00:00:00';
      this.queryParamsT.refundApplyTimeEnd = v[1] + ' 23:59:59';
    },
    edit(id) {
    },
    details(a, b) {
      if (b == 1) {
        //销售订单详情
        this.$refs.detail.showDialog(a);
      } else if (b == 2) {
        //退款单详情
        this.$refs.detailT.showDialog(a);
      } else if (b == 3) {
        //重新提交
        this.$refs.refunds.showDialog(a);
      }
    },
    del(id) {
    },
    add() {
      var now = new Date();
      var year = now.getFullYear();
      var month = now.getMonth() + 1;
      var date = now.getDate();
      var hour = now.getHours();
      var minute = now.getMinutes();
      var second = now.getSeconds();

      // 将单个数字前面补零
      month = month < 10 ? "0" + month : month;
      date = date < 10 ? "0" + date : date;
      hour = hour < 10 ? "0" + hour : hour;
      minute = minute < 10 ? "0" + minute : minute;
      second = second < 10 ? "0" + second : second;

      var currentTime =
        year +
        "-" +
        month +
        "-" +
        date +
        " " +
        hour +
        ":" +
        minute +
        ":" +
        second;

      this.$refs.add.showDialog(currentTime);
    },
    getList(v) {
      if (v == 1) {
        this.loading = true;
        let parmas = {
          refundSn: this.queryParams.refundSn,
          orderSn: this.queryParams.orderSn,
          name: this.queryParams.name,
          phone: this.queryParams.phone,
          addTimeStart: this.queryParams.addTimeStart,
          addTimeEnd: this.queryParams.addTimeEnd,
          orderStatus: this.queryParams.orderStatus,
          orderSourceList: this.queryParams.orderSourceList,
          userId: this.queryParams.userId,
          admissionName: this.queryParams.admissionName,
          // orderSource: this.queryParams.orderSource, //old
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize
        }
        console.log(parmas);
        list(parmas).then((response) => {
          this.list = response.data.rows;
          this.total = response.data.total;
          this.loading = false;
        });
      } else if (v == 2) {
        let parmas = {
          refundSn: this.queryParamsT.refundSn,
          orderSn: this.queryParamsT.orderSn,
          refundApplyContactsName: this.queryParamsT.refundApplyContactsName,
          refundApplyTimeStart: this.queryParamsT.refundApplyTimeStart,
          refundApplyTimeEnd: this.queryParamsT.refundApplyTimeEnd,
          orderStatus: this.queryParamsT.orderStatus,
          // orderSource: this.queryParamsT.orderSource, //old
          orderSourceList: this.queryParamsT.orderSourceList,
          userId: this.queryParamsT.userId,
          admissionName: this.queryParamsT.admissionName,
          refundSource: this.queryParamsT.refundSource,
          pageNum: this.queryParamsT.pageNum,
          pageSize: this.queryParamsT.pageSize
        }
        refundList(parmas).then((response) => {
          this.list = response.data.rows;
          this.total = response.data.total;
          this.loading = false;
        });
      }
    },
    /** 搜索 */
    handleQuery(v) {
      if (v == 1) {
        //销售订单
        this.getList(1);
      } else if (v == 2) {
        this.getList(2);
      }
    },
    /** 重置 */
    resetQuery(v) {
      if (v == 1) {
        for (var key in this.queryParams) {
          this.queryParams[key] = "";
        }
        this.times = [];
        this.queryParams.pageNum = 1;
        this.queryParams.pageSize = 10;
        this.getList(1);
      } else if (v == 2) {
        for (var key in this.queryParamsT) {
          this.queryParamsT[key] = "";
        }
        this.timesT = [];
        this.queryParamsT.pageNum = 1;
        this.queryParamsT.pageSize = 10;
        this.getList(2);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.pagination-container {
  padding: 0 !important;
}
</style>

