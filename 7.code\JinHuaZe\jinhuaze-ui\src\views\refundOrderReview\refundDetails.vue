<script>
import AddShowroom from "./components/addShowroom.vue";
import {orderDetail} from "@/api/order";
import {handleRefundCheck} from "@/api/refundOrderReview";


export default {
  components: {
    AddShowroom
  },
  name: "addDialog",
  data() {
    return {
      visible: false,
      orderSn: null,
      isManage: false,
      details: {
        orderAdmission: [],
        orderAdmissionDetail: [],
        refundDetail: {}
      },
      form: {
        roomName: ''
      }
    };
  },
  watch: {
    imgList(val) {
      this.form.img = val;
    }
  },
  mounted() {
    const { isManage, orderSn } = this.$route.query
    this.isManage = isManage
    this.orderSn = orderSn
    this.init()
  },
  methods: {
    open() {
      this.visible = true;
    },
    // 获取详情
    async init() {
      const {data} = await orderDetail({orderSn: this.orderSn})
      this.details = data
    },
    // 返回列表
    viewQrCode(url) {
      this.$alert(`<img src="${process.env.VUE_APP_FILE_URL + url}" style="margin: 0 auto;display: block" />`, '查看门票', {
        dangerouslyUseHTMLString: true
      });
    },
    // 保存表单
    async saveForm() {
      const { code } = await handleRefundCheck({
        id: this.details.id,
        refundStatus: '1', // 审批结果 1通过 2驳回
        rejectReason: '',
      }, this.isManage);
      if (code === 200) {
        this.handleClose()
      }
    },
    // 返回
    handleClose() {
      this.$tab.closeOpenPage()
      this.$router.go(-1);
    },
    // 驳回
    reject() {
      this.$refs.add.showDialog(this.details.id, this.isManage);
    }
  }
}
</script>

<template>
  <div class="content">
    <div class="content-body">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <div class="left-title">
            <div class="left-icon"></div>
            <span>审批详情</span>
          </div>
          <el-button type="warning" class="title-rightbtn" plain icon="el-icon-close" size="mini"
                     @click="handleClose">返回
          </el-button>
        </div>
        <div class="card-body">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <div class="left-title">
                <span class="right-second-label">退款单号：{{ details.refundSn }}</span>
                <div class="second-icon">
                  <el-tag size="small">{{ details.orderStatusText }}</el-tag>
                </div>
              </div>

              <div class="bottom-temp">
                <el-button type="primary" size="small" @click="saveForm">通过</el-button>
                <el-button type="primary" plain size="small" @click="reject">驳回</el-button>
              </div>
            </div>

            <div class="card-body">
              <el-descriptions title="" :column="3">
                <el-descriptions-item label="关联销售订单号">{{ details.orderSn }}</el-descriptions-item>
                <el-descriptions-item label="原订单金额">{{ details.actualAmount }}</el-descriptions-item>
                <el-descriptions-item label="订单类别">{{ details.orderCategoryText }}</el-descriptions-item>
                <el-descriptions-item label="订单来源">{{ details.orderSourceText }}</el-descriptions-item>
                <el-descriptions-item label="审批状态">待审批</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-card>
          <el-card class="box-card" style="margin-top:10px">
            <div slot="header" class="clearfix">
              <div class="left-title">
                <div class="left-icon"></div>
                <span>原订单购票信息</span>
              </div>
            </div>
            <div class="card-body">
              <el-descriptions title="" :column="3" v-for="(item,index) in details.orderAdmission" :key="index"
                               style="margin-bottom:10px">
                <el-descriptions-item label="门票名称">{{ item.admissionName }}</el-descriptions-item>
                <el-descriptions-item label="门票数量">{{ item.buyNum }}</el-descriptions-item>
                <el-descriptions-item label="门票价格">{{ item.admissionPrice }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-card>
          <el-card class="box-card" style="margin-top:10px">
            <div slot="header" class="clearfix">
              <div class="left-title">
                <div class="left-icon"></div>
                <span>退票门票信息</span>
              </div>
            </div>
            <div class="card-body">
              <el-descriptions title="" :column="3" v-for="(item,index) in details.orderAdmissionDetail" :key="index"
                               style="margin-bottom:10px">
                <el-descriptions-item label="游客姓名">{{ item.contactsName }}</el-descriptions-item>
                <el-descriptions-item label="手机号">{{ item.contactsPhone }}</el-descriptions-item>
                <el-descriptions-item label="证件类型">{{ item.contactsDocumentType == 1 ? '身份证' : '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="证件号码">{{ item.contactsCredentialNo }}</el-descriptions-item>
                <el-descriptions-item label="核销状态">{{ item.statusText }}</el-descriptions-item>
                <el-descriptions-item label="核销码">
                  <el-button style="padding: 3px 10px" type="text" size="small"
                             @click="viewQrCode(item.verifincationPicUrl)">查看门票
                  </el-button>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-card>
          <el-card class="box-card" style="margin-top:10px">
            <div slot="header" class="clearfix">
              <div class="left-title">
                <div class="left-icon"></div>
                <span>退款信息</span>
              </div>
            </div>
            <div class="card-body">
              <el-descriptions title="" :column="3">
                <el-descriptions-item label="退款申请人">{{ details.refundDetail.refundApplyContactsName }}
                </el-descriptions-item>
                <el-descriptions-item label="退款申请时间">{{ details.refundDetail.refundApplyTime }}
                </el-descriptions-item>
                <el-descriptions-item label="退款完成时间">{{ details.refundDetail.abc || '-' }}</el-descriptions-item>
                <el-descriptions-item label="退款方式">
                  {{ details.refundDetail.refundType == 1 ? '微信原路退回' : '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="退款金额">{{ details.refundDetail.refundAmount || '0.00' }}元
                </el-descriptions-item>
                <el-descriptions-item label="退款原因">{{ details.refundDetail.refundReason }}</el-descriptions-item>
                <el-descriptions-item label="退款说明">{{ details.refundDetail.refundExplain }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-card>
        </div>
      </el-card>
    </div>
    <AddShowroom ref="add" @success="handleClose"/>
  </div>
</template>

<style scoped lang="scss">
::v-deep .el-select {
  width: 100% !important;
}

::v-deep .el-card {
  border-radius: 10px;
  border: none;
}

::v-deep .el-dialog:not(.is-fullscreen) {
  margin-top: 15vh !important;
}

::v-deep .el-form-item__content {
  margin-left: 0 !important;
}

// ::v-deep .el-button--small {
//     padding: 0 !important;
//     padding-top: 4px !important;
//     display: flex;
//     align-items: center;
// }
.left-title {
  display: flex;
  align-items: center;
}

.clearfix {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.title-rightbtn {
  position: absolute;
  right: 0px;
}

.bottom-temp {
  position: absolute;
  right: 0;
  bottom: -3px;
  display: flex;
  justify-content: center;
  // margin-top: 20px;
}

.bottom-btn {
  display: flex;
  justify-content: center;
}

.top-content {
  display: flex;
  align-items: center;
}

.avatar-uploader {
  height: 130px;

  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
}

.avatar-uploader ::v-deep .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 128px;
  height: 128px;
  line-height: 128px;
  text-align: center;
}

.img-box {
  display: flex;
  gap: 10px;
}

.content-body {
  margin: 15px;

  .second-title {
    margin-left: 20px;
    margin-bottom: 20px;
    margin-right: 10px;
  }

  .box-card {
    display: flex;
    flex-direction: column;
    position: relative;

    .bottom-btn {
      position: absolute;
      bottom: 10px;
      left: 50%;
      right: 50%;
      width: 60px;
    }

    .clearfix {
      display: flex;
      align-items: center;

      span {
        font-weight: bold !important;
      }

      .left-icon {
        width: 3px;
        height: 16px;
        margin-right: 4px;
        margin-top: 2px;
        border-radius: 10px;
        background-color: rgb(0, 121, 254);
      }
    }
  }

  .title-btn {
    display: flex;
    align-items: flex-start;
    line-height: 34px;
  }

  .item-card {
    margin-bottom: 10px;
    margin-left: 20px;
    margin-right: 20px;
    position: relative;

    .delete {
      position: absolute;
      top: 0;
      right: 10px;
    }
  }
}

.second-icon {
  margin-left: 10px;
}

.statusText {
  padding: 0 !important;
}
</style>
