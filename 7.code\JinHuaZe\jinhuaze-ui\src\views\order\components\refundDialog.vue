<template>
  <common-dialog ref="dialog" width="41%" title="请输入退款信息" class="blueDialogScroll">
    <el-form class="forms" :model="formValues" :rules="rules" ref="form" size="small" label-width="110px">
        <el-form-item label="退款总金额" prop="refundAmount">
          <el-input v-model="formValues.refundAmount" v-number-input.decimal="formValues.refundAmount"   placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="退款原因" prop="refundReason">
          <el-select v-model="formValues.refundReason" style="width: 100%" placeholder="请选择" clearable>
            <el-option
              v-for="item in reasonList"
              :key="item.label"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="退款说明" prop="refundExplain">
          <el-input v-model="formValues.refundExplain" type="textarea" :rows="6" placeholder="请输入内容"></el-input>
        </el-form-item>
      <el-form-item class="form-footer">
        <el-button type="primary" @click="submitForm('form')" :disabled="loading" :loading="loading">
          {{ loading ? "提交中..." : "确认" }}
        </el-button>
        <el-button @click="quxiao">取消</el-button>
      </el-form-item>
    </el-form>

  </common-dialog>
</template>

<script>
import { webCreateRefund } from "@/api/order/index";
export default {
  name: 'refundDialog',
  data() {
    return {
      loading:false,
      formValues: {
        refundAmount: '',
        refundReason:'',
        refundExplain:'',
        orderSn:''
      },
      reasonList:[
          {
            label:'计划有变没空使用',
            value:'计划有变没空使用'
          },
          {
            label:'买多/买错了',
            value:'买多/买错了'
          },
          {
            label:'担心安全问题',
            value:'担心安全问题'
          },
          {
            label:'后悔不想要了',
            value:'后悔不想要了'
          },
          {
            label:'误下单',
            value:'误下单'
          },
          {
            label:'没看清规则',
            value:'没看清规则'
          },
          {
            label:'感觉麻烦限制多',
            value:'感觉麻烦限制多'
          },
          {
            label:'觉得不划算',
            value:'觉得不划算'
          },
          {
            label:'发现其他平台更优惠',
            value:'发现其他平台更优惠'
          },
          {
            label:'排队太久/预约不上',
            value:'排队太久/预约不上'
          },
          {
            label:'无法联系商家',
            value:'无法联系商家'
          },
          {
            label:'发现评价不好',
            value:'发现评价不好'
          },
          {
            label:'展厅不接待',
            value:'展厅不接待'
          },
          {
            label:'展厅服务不好',
            value:'展厅服务不好'
          },
          {
            label:'展厅因故停业',
            value:'展厅因故停业'
          },
          {
            label:'其他',
            value:'其他'
          },
      ],
      rules: {
        refundAmount: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        refundReason: [
          { required: true, message: '请选择', trigger: 'blur' },
        ],
        refundExplain: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
      }
    }
  },
  mounted() {

  },
  methods: {

    quxiao(){
      this.$refs.dialog.dialogVisible = false
    },
    submitForm(formName) {
      let params = {
        orderSn:this.formValues.orderSn,
        refundAmount:this.formValues.refundAmount,
        refundReason:this.formValues.refundReason,
        refundExplain:this.formValues.refundExplain,
      }
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            const res = await webCreateRefund(params)
            const { code } = res
            if (code === 200) {
              this.$message({
                message: '保存成功',
                type: 'success'
              })
              this.$emit('success')
              this.loading = false
              this.$refs.dialog.dialogVisible = false
              this.$refs[formName].resetFields()
            }
          } catch {
            this.loading = false
          }
        } else {
          return false;
        }
      });
    },
    showDialog(a) {
      this.formValues.orderSn =  a
      this.$refs.dialog.dialogVisible = true
      this.formValues.refundAmount =  ''
      this.formValues.refundReason =  ''
      this.formValues.refundExplain =  ''

    }

  }
}
</script>

<style lang="scss" scoped>

.mb{
  margin-bottom: 40px;
}
.mt{
  margin-top: -40px;
}

//行颜色
::v-deep .el-select-dropdown__item {
  color: #606266 !important;
}

//弹窗高度
::v-deep .el-dialog {
  height:auto !important;
}

//弹窗主体
::v-deep .el-dialog__body {
  overflow-y: auto !important;
}

//底部按钮
.form-footer {
  width: 100%;
  height: 65px;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  margin-bottom: 0;

  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
    margin-top: 15px;
  }
}

//上传图片
::v-deep .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  ::v-deep .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  ::v-deep .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  ::v-deep .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
</style>
