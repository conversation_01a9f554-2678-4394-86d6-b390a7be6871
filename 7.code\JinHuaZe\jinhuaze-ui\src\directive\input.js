/**
 * 输入限制指令
 * 使用方式：
 * v-input-limit.chinese  只允许汉字
 * v-input-limit.number   只允许数字
 * v-input-limit.phone    只允许手机号（数字，最多11位）
 * v-input-limit.price    只允许价格（数字和小数点，最多两位小数）
 */

export default {
  bind(el, binding, vnode) {
    const input = el.querySelector('input') || el
    const modifiers = binding.modifiers

    // 根据修饰符确定过滤规则
    let filterRule = null
    
    if (modifiers.chinese) {
      // 只允许汉字
      filterRule = (value) => value.replace(/[^\u4e00-\u9fa5]/g, '')
    } else if (modifiers.number) {
      // 只允许数字
      filterRule = (value) => value.replace(/\D/g, '')
    } else if (modifiers.phone) {
      // 只允许数字，最多11位
      filterRule = (value) => {
        const numbers = value.replace(/\D/g, '')
        return numbers.slice(0, 11)
      }
    } else if (modifiers.price) {
      // 只允许数字和小数点，最多两位小数
      filterRule = (value) => {
        if (!value) return value
        
        // 只保留数字和小数点
        let filteredValue = value.replace(/[^\d.]/g, '')
        
        // 处理多个小数点的情况
        const parts = filteredValue.split('.')
        if (parts.length > 2) {
          // 如果有多个小数点，只保留第一个小数点前的部分和第一个小数点后的部分
          filteredValue = parts[0] + '.' + parts.slice(1).join('')
        }
        
        // 如果有小数点，限制小数点后最多两位
        const dotIndex = filteredValue.indexOf('.')
        if (dotIndex !== -1) {
          const integerPart = filteredValue.substring(0, dotIndex)
          const decimalPart = filteredValue.substring(dotIndex + 1)
          
          // 限制小数点后最多两位
          const limitedDecimalPart = decimalPart.substring(0, 2)
          filteredValue = integerPart + '.' + limitedDecimalPart
        }
        
        return filteredValue
      }
    }

    if (filterRule) {
      let isComposing = false // 标记是否正在进行中文输入

      // 处理中文输入开始
      const handleCompositionStart = () => {
        isComposing = true
      }

      // 处理中文输入结束
      const handleCompositionEnd = (e) => {
        isComposing = false
        handleInputFilter(e)
      }

      // 处理输入过滤
      const handleInputFilter = (e) => {
        if (isComposing) return // 如果正在进行中文输入，不处理

        const originalValue = e.target.value
        const filteredValue = filterRule(originalValue)
        
        if (originalValue !== filteredValue) {
          // 保存光标位置
          const selectionStart = e.target.selectionStart
          const selectionEnd = e.target.selectionEnd
          
          // 设置过滤后的值
          e.target.value = filteredValue
          
          // 恢复光标位置（调整到合适位置）
          const newCursorPos = Math.min(selectionStart, filteredValue.length)
          e.target.setSelectionRange(newCursorPos, newCursorPos)
          
          // 手动触发Vue的响应式更新
          if (vnode.componentInstance) {
            vnode.componentInstance.$emit('input', filteredValue)
          } else if (vnode.data && vnode.data.model) {
            vnode.data.model.callback(filteredValue)
          }
        }
      }

      // 监听粘贴事件
      const handlePaste = (e) => {
        setTimeout(() => {
          if (!isComposing) {
            handleInputFilter(e)
          }
        }, 0)
      }

      input.addEventListener('compositionstart', handleCompositionStart)
      input.addEventListener('compositionend', handleCompositionEnd)
      input.addEventListener('input', handleInputFilter)
      input.addEventListener('paste', handlePaste)

      // 保存事件处理器，便于后续移除
      input._inputLimitHandlers = {
        handleCompositionStart,
        handleCompositionEnd,
        handleInputFilter,
        handlePaste
      }
    }
  },

  unbind(el) {
    const input = el.querySelector('input') || el
    if (input._inputLimitHandlers) {
      const handlers = input._inputLimitHandlers
      input.removeEventListener('compositionstart', handlers.handleCompositionStart)
      input.removeEventListener('compositionend', handlers.handleCompositionEnd)
      input.removeEventListener('input', handlers.handleInputFilter)
      input.removeEventListener('paste', handlers.handlePaste)
      delete input._inputLimitHandlers
    }
  }
}