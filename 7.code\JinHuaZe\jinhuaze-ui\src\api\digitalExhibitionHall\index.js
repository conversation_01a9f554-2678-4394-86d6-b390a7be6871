// /museum-main/digitalShowroomManage/detail
import request from "@/utils/request";

export function getDigitalExhibitionHallInfo() {
  return request({
    url: '/museum-main/digitalShowroomManage/detail',
    method: 'get'
  })
}

// /museum-main/digitalShowroomManage/submit
export function saveDigitalExhibitionHallInfo(data) {
  return request({
    url: '/museum-main/digitalShowroomManage/submit',
    method: 'post',
    data
  })
}
