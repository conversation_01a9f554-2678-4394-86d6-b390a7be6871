<template>
    <common-dialog ref="dialog" width="45%" title="驳回原因" class="blueDialogScroll">
        <el-form class="forms" :model="formValues" :rules="rules" ref="form" size="small" label-width="110px">
            <el-form-item label="" prop="rejectRemark">
                <el-input v-model="formValues.rejectRemark" placeholder="请输入" type="textarea"
                    :autosize="{ minRows: 4}"></el-input>
            </el-form-item>

            <el-form-item class="form-footer">
                <el-button type="primary" @click="saveForm('form')" :disabled="loading" :loading="loading">
                    {{ loading ? "提交中..." : "确定" }}
                </el-button>
                <el-button @click="quxiao">取消</el-button>
            </el-form-item>
        </el-form>

    </common-dialog>
</template>

<script>
import { add } from "@/api/configurationCenter/index";
import MyEditor from "@/components/WangEditor";
export default {
    name: 'addShowroom',
  props: {
    rejectRemark: String
  },
    components: {
        MyEditor
    },
    data () {
        return {
            loading: false,
            formValues: {
                rejectRemark: this.rejectRemark,
            },
            rules: {
                rejectRemark: [
                    { required: true, message: '请输入', trigger: 'blur' },
                ],
            }

        };
    },
  watch: {
    rejectRemark(val) {
      this.formValues.rejectRemark = val
    }
  },
    mounted () {

    },
    methods: {
        quxiao () {
            this.$refs.dialog.dialogVisible = false;
        },
        // 保存表单
        saveForm () {
            this.$emit('update:rejectRemark', this.formValues.rejectRemark);
            this.$emit('saveForm', this.formValues.rejectRemark);
        },
        showDialog () {
            this.$refs.dialog.dialogVisible = true;
            for (var key in this.formValues) {
                this.formValues[key] = '';
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.mb {
    margin-bottom: 40px;
}
.mt {
    margin-top: -40px;
}

//行颜色
::v-deep .el-select-dropdown__item {
    color: #606266 !important;
}

//弹窗高度
::v-deep .el-dialog {
    height: auto !important;
}

//弹窗主体
::v-deep .el-dialog__body {
    overflow-y: auto !important;
}

//底部按钮
.form-footer {
    width: 100%;
    height: 65px;
    text-align: center;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    margin-bottom: 0;

    ::v-deep .el-form-item__content {
        margin-left: 0 !important;
        margin-top: 15px;
    }
}

//上传图片
::v-deep .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
::v-deep .avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
::v-deep .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
}
::v-deep .avatar {
    width: 100px;
    height: 100px;
    display: block;
}
</style>
